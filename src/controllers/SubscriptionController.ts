import { Response } from 'express';
import Subscription from '../models/subscription.model';
import Student from '../models/student';
import <PERSON><PERSON> from '../models/tutor';
import { AuthRequest } from '../types/AuthRequest';
import { Types } from 'mongoose';
import Stripe from 'stripe';
import SubscriptionService from '../services/subscriptionService';
import { CardTokenizationService } from '../services/cardTokenizationService';
import { createErrorResponse } from '../middlewares/errorHandler';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: '2025-05-28.basil',
});

export class SubscriptionController {
  
  // Get all subscriptions for a student
 // Get subscriptions by student ID from URL params
// Route: GET /api/students/:studentId/subscriptions

async getStudentSubscriptions(req: AuthRequest, res: Response): Promise<void> {
  try {
    const { studentId } = req.params;
    const { status, page = 1, limit = 10 } = req.query;

    // Check if user is authenticated
    if (!req.user || !req.user._id) {
      res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
      return;
    }

    if (!studentId) {
      res.status(400).json({
        success: false,
        error: 'Student ID is required'
      });
      return;
    }

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(studentId)) {
      res.status(400).json({
        success: false,
        error: 'Invalid student ID format'
      });
      return;
    }

    // Verify the student exists
    const student = await Student.findById(studentId);
    if (!student) {
      res.status(404).json({
        success: false,
        error: 'Student not found'
      });
      return;
    }

    // Build query
    const query: any = { studentId };
    if (status && ['active', 'paused', 'cancelled', 'pending_transfer', 'incomplete'].includes(status as string)) {
      query.status = status;
    }

    // Pagination
    const skip = (Number(page) - 1) * Number(limit);

    const [subscriptions, total] = await Promise.all([
      Subscription.find(query)
        .populate('tutorId', 'basePrice teachingSubjects rating totalLessons firstname lastname avatar')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(Number(limit)),
      Subscription.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: subscriptions,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching student subscriptions:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}

async createSubscription(req: AuthRequest, res: Response): Promise<void> {
  try {
    const { tutorId, lessonsPerWeek, paymentMethodId, email, cardToken, cardDetails, secureToken } = req.body;

    // Check if user is authenticated
    if (!req.user || !req.user._id) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    // Enhanced validation
    if (!tutorId || !lessonsPerWeek || !email) {
      createErrorResponse(res, 'Missing required fields: tutorId, lessonsPerWeek, email', 400);
      return;
    }

    if (!paymentMethodId && !cardToken && !cardDetails && !secureToken) {
      createErrorResponse(res, 'Payment method required: provide paymentMethodId, cardToken, cardDetails, or secureToken', 400);
      return;
    }

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(tutorId)) {
      res.status(400).json({
        success: false,
        error: 'Invalid tutor ID format'
      });
      return;
    }

    // Validate lessons per week
    if (![1, 2, 3, 5].includes(lessonsPerWeek)) {
      res.status(400).json({
        success: false,
        error: 'Invalid lessons per week value. Allowed: 1, 2, 3, 5'
      });
      return;
    }

    // Validate email format
    const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
    if (!emailRegex.test(email)) {
      res.status(400).json({
        success: false,
        error: 'Invalid email format'
      });
      return;
    }

    // Get student from authenticated user or look up by email
    let student;
    if (req.user.role === 'student') {
      student = req.user;
    } else {
      // Look up by email for non-student users creating subscriptions
      student = await Student.findOne({ email });
      if (!student) {
        res.status(404).json({
          success: false,
          error: 'Student profile not found'
        });
        return;
      }
    }

    // Check for existing active subscriptions
    const activeSubscription = await Subscription.findOne({
      studentId: student._id,
      status: { $in: ['active', 'pending_transfer'] }
    });

    if (activeSubscription) {
      res.status(400).json({
        success: false,
        error: 'You already have an active or pending subscription',
        data: { existingSubscriptionId: activeSubscription._id }
      });
      return;
    }

    const tutor = await Tutor.findById(tutorId);
    if (!tutor) {
      res.status(404).json({
        success: false,
        error: 'Tutor not found'
      });
      return;
    }

    if (tutor.approvalStatus !== 'approved') {
      res.status(400).json({
        success: false,
        error: 'Tutor is not approved for tutoring'
      });
      return;
    }

    if (!tutor.isActive) {
      res.status(400).json({
        success: false,
        error: 'Tutor is currently inactive'
      });
      return;
    }

    const priceMap: { [key: number]: number } = {
      1: 12000,
      2: 22000,
      3: 31000,
      5: 48000
    };

    const monthlyPriceCents = priceMap[lessonsPerWeek];
    if (!monthlyPriceCents) {
      res.status(400).json({ error: 'Invalid lessons per week value. Allowed: 1, 2, 3, 5' });
      return;
    }

    const monthlyPrice = monthlyPriceCents / 100;

    let stripeCustomer: Stripe.Customer;
    
    // Fixed customer retrieval with proper error handling
    if (student.stripeCustomerId) {
      try {
        const retrievedCustomer = await stripe.customers.retrieve(student.stripeCustomerId);
        
        // Check if customer was deleted
        if (retrievedCustomer.deleted) {
          console.log(`Customer ${student.stripeCustomerId} was deleted, creating new one`);
          // Clear the invalid customer ID and create a new one
          student.stripeCustomerId = undefined;
        } else {
          stripeCustomer = retrievedCustomer as Stripe.Customer;
        }
      } catch (error: any) {
        console.log(`Error retrieving customer ${student.stripeCustomerId}:`, error.message);
        // If customer doesn't exist, clear the invalid ID
        if (error.code === 'resource_missing') {
          student.stripeCustomerId = undefined;
        } else {
          throw error; // Re-throw if it's a different error
        }
      }
    }

    // Create new customer if we don't have a valid one
    if (!stripeCustomer!) {
      stripeCustomer = await stripe.customers.create({
        email: student.email,
        name: student.firstname,
        metadata: {
          studentId: (student._id as Types.ObjectId).toString(),
          userId: (student as any).userId?.toString() || ''
        }
      });
      student.stripeCustomerId = stripeCustomer.id;
      await student.save();
    }

    let finalPaymentMethodId: string;

    if (paymentMethodId) {
      // Direct payment method ID provided
      finalPaymentMethodId = paymentMethodId;
    } else if (secureToken) {
      // Use secure tokenized card
      const tokenResult = await CardTokenizationService.createPaymentMethodFromToken(
        secureToken,
        stripeCustomer.id
      );

      if (!tokenResult.success || !tokenResult.paymentMethodId) {
        createErrorResponse(res, tokenResult.error || 'Invalid secure token', 400);
        return;
      }

      finalPaymentMethodId = tokenResult.paymentMethodId;
    } else if (cardToken) {
      // Use Stripe card token (recommended approach)
      const paymentMethod = await stripe.paymentMethods.create({
        type: 'card',
        card: { token: cardToken }
      });
      finalPaymentMethodId = paymentMethod.id;
    } else if (cardDetails) {
      // Tokenize card details securely first
      const tokenizationResult = await CardTokenizationService.tokenizeCard(
        {
          number: cardDetails.number.replace(/\s/g, ''),
          exp_month: parseInt(cardDetails.exp_month),
          exp_year: parseInt(cardDetails.exp_year),
          cvc: cardDetails.cvc.toString(),
          name: cardDetails.name || `${student.firstname} ${student.lastname}`,
          address_line1: cardDetails.address_line1,
          address_line2: cardDetails.address_line2,
          address_city: cardDetails.address_city,
          address_state: cardDetails.address_state,
          address_zip: cardDetails.address_zip,
          address_country: cardDetails.address_country || 'US'
        },
        stripeCustomer.id
      );

      if (!tokenizationResult.success || !tokenizationResult.paymentMethodId) {
        createErrorResponse(res, tokenizationResult.error || 'Failed to process card details', 400);
        return;
      }

      finalPaymentMethodId = tokenizationResult.paymentMethodId;
    } else {
      createErrorResponse(res, 'No valid payment method provided', 400);
      return;
    }

    // Attach payment method to customer
    await stripe.paymentMethods.attach(finalPaymentMethodId, {
      customer: stripeCustomer.id,
    });

    const productName = `Tutoring Subscription - ${lessonsPerWeek} Lessons Weekly`;
    const existingProducts = await stripe.products.list({ limit: 100, active: true });
    let stripeProduct = existingProducts.data.find(p => p.metadata.lessonsPerWeek === lessonsPerWeek.toString());

    if (!stripeProduct) {
      stripeProduct = await stripe.products.create({
        name: productName,
        description: `Weekly tutoring subscription with ${lessonsPerWeek} lessons per week`,
        metadata: { lessonsPerWeek: lessonsPerWeek.toString() }
      });
    }

    const existingPrices = await stripe.prices.list({ product: stripeProduct.id, active: true });
    let stripePrice = existingPrices.data.find(p => p.unit_amount === monthlyPriceCents && p.currency === 'usd');

    if (!stripePrice) {
      stripePrice = await stripe.prices.create({
        product: stripeProduct.id,
        unit_amount: monthlyPriceCents,
        currency: 'usd',
        recurring: { interval: 'month' },
        metadata: { lessonsPerWeek: lessonsPerWeek.toString() }
      });
    }

    // FIXED: Create subscription with immediate payment behavior and proper configuration
    const stripeSubscription = await stripe.subscriptions.create({
      customer: stripeCustomer.id,
      items: [{ price: stripePrice.id }],
      default_payment_method: finalPaymentMethodId,
      payment_behavior: 'default_incomplete', // Keep as default_incomplete to handle 3D Secure
      payment_settings: {
        save_default_payment_method: 'on_subscription',
        payment_method_types: ['card']
      },
      expand: ['latest_invoice.payment_intent', 'pending_setup_intent'],
      metadata: {
        tutorId: tutorId,
        studentId: (student._id as Types.ObjectId).toString(),
        lessonsPerWeek: lessonsPerWeek.toString()
      },
      proration_behavior: 'create_prorations'
    });

    // FIXED: Handle incomplete subscriptions properly with retry logic
    if (stripeSubscription.status === 'incomplete') {
      let paymentIntent: Stripe.PaymentIntent | null = null;
      let setupIntent: Stripe.SetupIntent | null = null;
      let latestInvoice: Stripe.Invoice | null = null;

      // Method 1: Check expanded objects first
      if (stripeSubscription.latest_invoice && typeof stripeSubscription.latest_invoice === 'object') {
        latestInvoice = stripeSubscription.latest_invoice as Stripe.Invoice;
        if ((latestInvoice as any).payment_intent && typeof (latestInvoice as any).payment_intent === 'object') {
          paymentIntent = (latestInvoice as any).payment_intent as Stripe.PaymentIntent;
        }
      }

      if (!paymentIntent && stripeSubscription.pending_setup_intent && typeof stripeSubscription.pending_setup_intent === 'object') {
        setupIntent = stripeSubscription.pending_setup_intent as Stripe.SetupIntent;
      }

      // Method 2: If not found, try fetching invoice separately
      if (!paymentIntent && !setupIntent && stripeSubscription.latest_invoice) {
        try {
          const invoiceId = typeof stripeSubscription.latest_invoice === 'string'
            ? stripeSubscription.latest_invoice
            : stripeSubscription.latest_invoice.id;

          if (invoiceId) {
            latestInvoice = await stripe.invoices.retrieve(invoiceId, {
              expand: ['payment_intent']
            });

            if ((latestInvoice as any).payment_intent && typeof (latestInvoice as any).payment_intent === 'object') {
              paymentIntent = (latestInvoice as any).payment_intent as Stripe.PaymentIntent;
            }
          }
        } catch (invoiceError) {
          console.error('Error fetching invoice:', invoiceError);
        }
      }

      // Method 3: If still not found, wait and retry (for timing issues)
      if (!paymentIntent && !setupIntent) {
        console.log('Payment/Setup intent not found, retrying after 2 seconds...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        try {
          const refreshedSubscription = await stripe.subscriptions.retrieve(stripeSubscription.id, {
            expand: ['latest_invoice.payment_intent', 'pending_setup_intent']
          });
          
          if (refreshedSubscription.latest_invoice && typeof refreshedSubscription.latest_invoice === 'object') {
            latestInvoice = refreshedSubscription.latest_invoice as Stripe.Invoice;
            if ((latestInvoice as any).payment_intent && typeof (latestInvoice as any).payment_intent === 'object') {
              paymentIntent = (latestInvoice as any).payment_intent as Stripe.PaymentIntent;
            }
          }
          
          if (!paymentIntent && refreshedSubscription.pending_setup_intent && typeof refreshedSubscription.pending_setup_intent === 'object') {
            setupIntent = refreshedSubscription.pending_setup_intent as Stripe.SetupIntent;
          }
        } catch (retryError) {
          console.error('Error on retry:', retryError);
        }
      }

      // Method 4: Try to manually finalize the subscription if possible
      if (!paymentIntent && !setupIntent) {
        try {
          console.log('Attempting to finalize incomplete subscription...');
          
          // Try to finalize the subscription
          const finalizedSubscription = await stripe.subscriptions.update(stripeSubscription.id, {
            payment_behavior: 'error_if_incomplete',
            expand: ['latest_invoice.payment_intent']
          });

          if (finalizedSubscription.status === 'active') {
            // Subscription was successfully activated
            console.log('Subscription successfully finalized');
            
            // Get payment intent from finalized subscription
            if (finalizedSubscription.latest_invoice && typeof finalizedSubscription.latest_invoice === 'object') {
              latestInvoice = finalizedSubscription.latest_invoice as Stripe.Invoice;
              if ((latestInvoice as any).payment_intent && typeof (latestInvoice as any).payment_intent === 'object') {
                paymentIntent = (latestInvoice as any).payment_intent as Stripe.PaymentIntent;
              }
            }

            // Create successful subscription record
            const currentTime = new Date();
            const futureTime = new Date(currentTime.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days from now

            const currentPeriodStart = SubscriptionService.safeTimestampToDate(
              (finalizedSubscription as any).current_period_start, currentTime
            );
            const currentPeriodEnd = SubscriptionService.safeTimestampToDate(
              (finalizedSubscription as any).current_period_end, futureTime
            );

            const subscription = new Subscription({
              studentId: student._id,
              tutorId: tutor._id,
              stripeSubscriptionId: finalizedSubscription.id,
              stripeCustomerId: stripeCustomer.id,
              stripePriceId: stripePrice.id,
              stripePaymentMethodId: finalPaymentMethodId,
              planType: `${lessonsPerWeek}_lessons_weekly`,
              lessonsPerWeek,
              monthlyPrice,
              status: 'active',
              currentPeriodStart,
              currentPeriodEnd,
              nextBillingDate: currentPeriodEnd,
              remainingLessons: lessonsPerWeek * 4,
              autoRenew: true,
              paymentHistory: paymentIntent ? [{
                amount: monthlyPrice,
                currency: 'usd',
                status: paymentIntent.status,
                stripePaymentIntentId: paymentIntent.id,
                stripeInvoiceId: latestInvoice?.id || '',
                paidAt: new Date(),
                description: `Initial payment for ${lessonsPerWeek} lessons weekly subscription`
              }] : []
            });

            await subscription.save();

            const paymentMethodDetails = await stripe.paymentMethods.retrieve(finalPaymentMethodId);
            const populatedSubscription = await Subscription.findById(subscription._id)
              .populate('tutorId', 'basePrice teachingSubjects firstname lastname avatar');

            res.status(201).json({
              success: true,
              message: 'Subscription created successfully',
              data: {
                subscription: populatedSubscription,
                stripeSubscriptionId: finalizedSubscription.id,
                paymentMethod: {
                  id: paymentMethodDetails.id,
                  type: paymentMethodDetails.type,
                  card: paymentMethodDetails.card ? {
                    brand: paymentMethodDetails.card.brand,
                    last4: paymentMethodDetails.card.last4,
                    exp_month: paymentMethodDetails.card.exp_month,
                    exp_year: paymentMethodDetails.card.exp_year
                  } : null
                },
                nextBillingDate: currentPeriodEnd,
                amountPaid: paymentIntent ? monthlyPrice : 0
              }
            });
            return;
          }
        } catch (finalizeError: any) {
          console.log('Could not finalize subscription:', finalizeError.message);
          // Continue to handle as incomplete
        }
      }

      // Now handle the different scenarios
      if (paymentIntent) {
        if (paymentIntent.status === 'requires_action' || paymentIntent.status === 'requires_confirmation') {
          // Create a pending subscription record for tracking
          const pendingSubscription = new Subscription({
            studentId: student._id,
            tutorId: tutor._id,
            stripeSubscriptionId: stripeSubscription.id,
            stripeCustomerId: stripeCustomer.id,
            stripePriceId: stripePrice.id,
            stripePaymentMethodId: finalPaymentMethodId,
            planType: `${lessonsPerWeek}_lessons_weekly`,
            lessonsPerWeek,
            monthlyPrice,
            status: 'pending_transfer',
            currentPeriodStart: new Date((stripeSubscription as any).current_period_start * 1000),
            currentPeriodEnd: new Date((stripeSubscription as any).current_period_end * 1000),
            nextBillingDate: new Date((stripeSubscription as any).current_period_end * 1000),
            remainingLessons: 0,
            autoRenew: true,
            paymentHistory: []
          });

          await pendingSubscription.save();

          res.status(200).json({
            success: false,
            requiresAction: true,
            clientSecret: paymentIntent.client_secret,
            subscriptionId: stripeSubscription.id,
            message: 'Payment requires additional authentication'
          });
          return;
        }

        if (paymentIntent.status === 'requires_payment_method') {
          await stripe.subscriptions.cancel(stripeSubscription.id);
          res.status(400).json({ 
            error: 'Payment method was declined. Please try a different payment method.',
            code: 'payment_method_declined'
          });
          return;
        }

        if (paymentIntent.status === 'succeeded') {
          // This shouldn't happen with incomplete subscription, but handle it
          console.log('Payment succeeded but subscription still incomplete, updating subscription...');
          try {
            const updatedSubscription = await stripe.subscriptions.retrieve(stripeSubscription.id);
            if (updatedSubscription.status === 'active') {
              // Proceed with active subscription logic
              // (This will be handled by the active subscription section below)
            }
          } catch (updateError) {
            console.error('Error checking updated subscription:', updateError);
          }
        }
      }

      if (setupIntent) {
        if (setupIntent.status === 'requires_action' || setupIntent.status === 'requires_confirmation') {
          res.status(200).json({
            success: false,
            requiresAction: true,
            clientSecret: setupIntent.client_secret,
            subscriptionId: stripeSubscription.id,
            setupIntent: true,
            message: 'Setup requires additional authentication'
          });
          return;
        }
      }

      // Handle case where invoice is open but no payment intent yet
      if (!paymentIntent && !setupIntent && latestInvoice && latestInvoice.status === 'open' && latestInvoice.id) {
        console.log('Invoice is open, attempting to pay it...');
        try {
          // Try to pay the open invoice
          const paidInvoice = await stripe.invoices.pay(latestInvoice.id);

          if (paidInvoice.status === 'paid') {
            // Invoice was successfully paid, check subscription status
            const updatedSubscription = await stripe.subscriptions.retrieve(stripeSubscription.id);
            if (updatedSubscription.status === 'active') {
              // Create successful subscription record
              const currentTime = new Date();
              const futureTime = new Date(currentTime.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days from now

              const currentPeriodStart = SubscriptionService.safeTimestampToDate(
                (updatedSubscription as any).current_period_start, currentTime
              );
              const currentPeriodEnd = SubscriptionService.safeTimestampToDate(
                (updatedSubscription as any).current_period_end, futureTime
              );

              const subscription = new Subscription({
                studentId: student._id,
                tutorId: tutor._id,
                stripeSubscriptionId: updatedSubscription.id,
                stripeCustomerId: stripeCustomer.id,
                stripePriceId: stripePrice.id,
                stripePaymentMethodId: finalPaymentMethodId,
                planType: `${lessonsPerWeek}_lessons_weekly`,
                lessonsPerWeek,
                monthlyPrice,
                status: 'active',
                currentPeriodStart,
                currentPeriodEnd,
                nextBillingDate: currentPeriodEnd,
                remainingLessons: lessonsPerWeek * 4,
                autoRenew: true,
                paymentHistory: (paidInvoice as any).payment_intent ? [{
                  amount: monthlyPrice,
                  currency: 'usd',
                  status: 'succeeded',
                  stripePaymentIntentId: (paidInvoice as any).payment_intent,
                  stripeInvoiceId: paidInvoice.id,
                  paidAt: new Date(),
                  description: `Initial payment for ${lessonsPerWeek} lessons weekly subscription`
                }] : []
              });

              await subscription.save();

              const paymentMethodDetails = await stripe.paymentMethods.retrieve(finalPaymentMethodId);
              const populatedSubscription = await Subscription.findById(subscription._id)
                .populate('tutorId', 'basePrice teachingSubjects firstname lastname avatar');

              res.status(201).json({
                success: true,
                message: 'Subscription created successfully',
                data: {
                  subscription: populatedSubscription,
                  stripeSubscriptionId: updatedSubscription.id,
                  paymentMethod: {
                    id: paymentMethodDetails.id,
                    type: paymentMethodDetails.type,
                    card: paymentMethodDetails.card ? {
                      brand: paymentMethodDetails.card.brand,
                      last4: paymentMethodDetails.card.last4,
                      exp_month: paymentMethodDetails.card.exp_month,
                      exp_year: paymentMethodDetails.card.exp_year
                    } : null
                  },
                  nextBillingDate: currentPeriodEnd,
                  amountPaid: monthlyPrice
                }
              });
              return;
            }
          }
        } catch (payError: any) {
          console.error('Error paying open invoice:', payError.message);
          // Continue to handle as incomplete
        }
      }

      // Last resort: If we still don't have clear next steps
      if (!paymentIntent && !setupIntent) {
        console.error('Subscription incomplete without clear next steps after all retries:', {
          subscriptionId: stripeSubscription.id,
          subscriptionStatus: stripeSubscription.status,
          latestInvoiceId: latestInvoice?.id,
          invoiceStatus: latestInvoice?.status
        });

        // Try one more approach: check if this is a $0 subscription (trial/free)
        if (monthlyPriceCents === 0 || (latestInvoice && latestInvoice.amount_due === 0)) {
          console.log('Detected $0 subscription, attempting to activate...');
          try {
            const activatedSub = await stripe.subscriptions.update(stripeSubscription.id, {
              trial_end: 'now'
            });

            if (activatedSub.status === 'active') {
              // Handle as active subscription
              console.log('Successfully activated $0 subscription');
              // The active subscription handler below will take care of this
            }
          } catch (activationError) {
            console.error('Failed to activate $0 subscription:', activationError);
          }
        }

        // Create incomplete subscription record for tracking
        const currentTime = new Date();
        const futureTime = new Date(currentTime.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days from now

        const incompleteSubscription = new Subscription({
          studentId: student._id,
          tutorId: tutor._id,
          stripeSubscriptionId: stripeSubscription.id,
          stripeCustomerId: stripeCustomer.id,
          stripePriceId: stripePrice.id,
          stripePaymentMethodId: finalPaymentMethodId,
          planType: `${lessonsPerWeek}_lessons_weekly`,
          lessonsPerWeek,
          monthlyPrice,
          status: 'incomplete',
          currentPeriodStart: SubscriptionService.safeTimestampToDate(
            (stripeSubscription as any).current_period_start, currentTime
          ),
          currentPeriodEnd: SubscriptionService.safeTimestampToDate(
            (stripeSubscription as any).current_period_end, futureTime
          ),
          nextBillingDate: SubscriptionService.safeTimestampToDate(
            (stripeSubscription as any).current_period_end, futureTime
          ),
          remainingLessons: 0,
          autoRenew: true,
          paymentHistory: []
        });

        await incompleteSubscription.save();

        res.status(400).json({
          success: false,
          error: 'Subscription setup incomplete. Please check your payment method and try again.',
          suggestion: 'Try using a different payment method or contact your bank if the issue persists.',
          data: {
            subscriptionId: stripeSubscription.id,
            incompleteSubscriptionId: incompleteSubscription._id
          },
          debug: {
            subscriptionStatus: stripeSubscription.status,
            invoiceStatus: latestInvoice?.status,
            invoiceId: latestInvoice?.id
          }
        });
        return;
      }
    }

    // FIXED: Handle active/trialing subscriptions
    if (stripeSubscription.status === 'active' || stripeSubscription.status === 'trialing') {
      let paymentIntent: Stripe.PaymentIntent | null = null;
      let latestInvoice: Stripe.Invoice | null = null;

      // Get the latest invoice and payment intent
      if (stripeSubscription.latest_invoice && typeof stripeSubscription.latest_invoice === 'object') {
        latestInvoice = stripeSubscription.latest_invoice as Stripe.Invoice;
        if ((latestInvoice as any).payment_intent && typeof (latestInvoice as any).payment_intent === 'object') {
          paymentIntent = (latestInvoice as any).payment_intent as Stripe.PaymentIntent;
        }
      }

      // If no payment intent found, try fetching separately
      if (!paymentIntent && stripeSubscription.latest_invoice) {
        try {
          const invoiceId = typeof stripeSubscription.latest_invoice === 'string'
            ? stripeSubscription.latest_invoice
            : stripeSubscription.latest_invoice.id;

          if (invoiceId) {
            latestInvoice = await stripe.invoices.retrieve(invoiceId, {
              expand: ['payment_intent']
            });

            if ((latestInvoice as any).payment_intent && typeof (latestInvoice as any).payment_intent === 'object') {
              paymentIntent = (latestInvoice as any).payment_intent as Stripe.PaymentIntent;
            }
          }
        } catch (invoiceError) {
          console.error('Error fetching invoice:', invoiceError);
        }
      }

      const currentTime = new Date();
      const futureTime = new Date(currentTime.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days from now

      const currentPeriodStart = SubscriptionService.safeTimestampToDate(
        (stripeSubscription as any).current_period_start, currentTime
      );
      const currentPeriodEnd = SubscriptionService.safeTimestampToDate(
        (stripeSubscription as any).current_period_end, futureTime
      );

      // Create subscription record
      const subscription = new Subscription({
        studentId: student._id,
        tutorId: tutor._id,
        stripeSubscriptionId: stripeSubscription.id,
        stripeCustomerId: stripeCustomer.id,
        stripePriceId: stripePrice.id,
        stripePaymentMethodId: finalPaymentMethodId,
        planType: `${lessonsPerWeek}_lessons_weekly`,
        lessonsPerWeek,
        monthlyPrice,
        status: 'active',
        currentPeriodStart,
        currentPeriodEnd,
        nextBillingDate: currentPeriodEnd,
        remainingLessons: lessonsPerWeek * 4,
        autoRenew: true,
        paymentHistory: paymentIntent ? [{
          amount: monthlyPrice,
          currency: 'usd',
          status: paymentIntent.status,
          stripePaymentIntentId: paymentIntent.id,
          stripeInvoiceId: latestInvoice?.id || '',
          paidAt: new Date(),
          description: `Initial payment for ${lessonsPerWeek} lessons weekly subscription`
        }] : []
      });

      await subscription.save();

      const paymentMethodDetails = await stripe.paymentMethods.retrieve(finalPaymentMethodId);
      const populatedSubscription = await Subscription.findById(subscription._id)
        .populate('tutorId', 'basePrice teachingSubjects firstname lastname avatar');

      res.status(201).json({
        success: true,
        message: 'Subscription created successfully',
        data: {
          subscription: populatedSubscription,
          stripeSubscriptionId: stripeSubscription.id,
          paymentMethod: {
            id: paymentMethodDetails.id,
            type: paymentMethodDetails.type,
            card: paymentMethodDetails.card ? {
              brand: paymentMethodDetails.card.brand,
              last4: paymentMethodDetails.card.last4,
              exp_month: paymentMethodDetails.card.exp_month,
              exp_year: paymentMethodDetails.card.exp_year
            } : null
          },
          nextBillingDate: currentPeriodEnd,
          amountPaid: paymentIntent ? monthlyPrice : 0
        }
      });
      return;
    }

    // Handle any other subscription status
    console.error('Unexpected subscription status:', stripeSubscription.status);
    await stripe.subscriptions.cancel(stripeSubscription.id);
    res.status(400).json({ 
      error: 'Unexpected subscription status. Please try again.',
      debug: {
        subscriptionStatus: stripeSubscription.status,
        subscriptionId: stripeSubscription.id
      }
    });

  } catch (error: any) {
    console.error('Error creating subscription:', error);
    if (error instanceof Stripe.errors.StripeError) {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}
  // Helper method to handle subscription confirmation (for cases requiring 3D Secure)
  async confirmSubscription(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { subscriptionId, paymentIntentId } = req.body;

      // Check if user is authenticated
      if (!req.user || !req.user._id) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      if (!subscriptionId) {
        res.status(400).json({ error: 'subscriptionId is required' });
        return;
      }

      // Retrieve subscription with payment intent
      const stripeSubscription = await stripe.subscriptions.retrieve(subscriptionId, {
        expand: ['latest_invoice.payment_intent']
      });

      const latestInvoice = stripeSubscription.latest_invoice as Stripe.Invoice;
      const paymentIntent = (latestInvoice as any).payment_intent as Stripe.PaymentIntent;

      // If specific payment intent ID provided, retrieve it
      let targetPaymentIntent = paymentIntent;
      if (paymentIntentId) {
        targetPaymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
      }

      if (targetPaymentIntent.status === 'succeeded') {
        // Update local subscription status
        const updatedSubscription = await Subscription.findOneAndUpdate(
          { stripeSubscriptionId: subscriptionId },
          { 
            status: 'active',
            $push: {
              paymentHistory: {
                amount: targetPaymentIntent.amount / 100,
                currency: targetPaymentIntent.currency,
                status: 'succeeded',
                stripePaymentIntentId: targetPaymentIntent.id,
                stripeInvoiceId: latestInvoice.id,
                paidAt: new Date(),
                description: 'Payment confirmed after 3D Secure authentication'
              }
            }
          },
          { new: true }
        ).populate('tutorId', 'basePrice teachingSubjects firstname lastname avatar');

        res.json({
          success: true,
          message: 'Credit card payment confirmed successfully',
          data: {
            subscription: updatedSubscription,
            paymentStatus: targetPaymentIntent.status
          }
        });
      } else {
        res.status(400).json({
          error: 'Credit card payment confirmation failed',
          status: targetPaymentIntent.status,
          message: 'Please try again or use a different payment method'
        });
      }
    } catch (error) {
      console.error('Error confirming subscription:', error);
      
      if (error instanceof Stripe.errors.StripeError) {
        res.status(400).json({
          error: 'Payment confirmation failed',
          details: error.message
        });
        return;
      }
      
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // Pause subscription
  async pauseSubscription(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { subscriptionId } = req.params;

      // Check if user is authenticated
      if (!req.user || !req.user._id) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      // Validate subscription ID format
      if (!subscriptionId || !Types.ObjectId.isValid(subscriptionId)) {
        res.status(400).json({
          success: false,
          error: 'Invalid subscription ID format'
        });
        return;
      }

      // Get student from authenticated user
      let student;
      if (req.user.role === 'student') {
        student = req.user;
      } else {
        // Allow non-students to pause subscriptions by looking up the student
        const subscription = await Subscription.findById(subscriptionId);
        if (!subscription) {
          res.status(404).json({
            success: false,
            error: 'Subscription not found'
          });
          return;
        }
        student = await Student.findById(subscription.studentId);
        if (!student) {
          res.status(404).json({
            success: false,
            error: 'Student not found'
          });
          return;
        }
      }

      const subscription = await Subscription.findById(subscriptionId);

      if (!subscription) {
        res.status(404).json({
          success: false,
          error: 'Subscription not found'
        });
        return;
      }

      if (subscription.status !== 'active') {
        res.status(400).json({
          success: false,
          error: `Cannot pause subscription with status '${subscription.status}'. Only active subscriptions can be paused.`
        });
        return;
      }

      // Pause the subscription in Stripe
      if (subscription.stripeSubscriptionId) {
        try {
          await stripe.subscriptions.update(subscription.stripeSubscriptionId, {
            pause_collection: {
              behavior: 'keep_as_draft'
            }
          });
        } catch (stripeError: any) {
          console.error('Error pausing subscription in Stripe:', stripeError);
          res.status(400).json({
            success: false,
            error: 'Failed to pause subscription in payment system. Please try again.'
          });
          return;
        }
      }

      // Update local subscription status
      await SubscriptionService.updateSubscriptionStatus(
        subscription._id.toString(),
        { status: 'paused' },
        'controller'
      );

      const updatedSubscription = await Subscription.findById(subscription._id)
        .populate('tutorId', 'basePrice teachingSubjects firstname lastname avatar');

      res.json({
        success: true,
        message: 'Subscription paused successfully',
        data: updatedSubscription
      });
    } catch (error) {
      console.error('Error pausing subscription:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Resume subscription
  async resumeSubscription(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { subscriptionId } = req.params;

      // Check if user is authenticated
      if (!req.user || !req.user._id) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      // Validate subscription ID format
      if (!subscriptionId || !Types.ObjectId.isValid(subscriptionId)) {
        res.status(400).json({
          success: false,
          error: 'Invalid subscription ID format'
        });
        return;
      }

      // Get student from authenticated user
      let student;
      if (req.user.role === 'student') {
        student = req.user;
      } else {
        // Allow non-students to resume subscriptions by looking up the student
        const subscription = await Subscription.findById(subscriptionId);
        if (!subscription) {
          res.status(404).json({
            success: false,
            error: 'Subscription not found'
          });
          return;
        }
        student = await Student.findById(subscription.studentId);
        if (!student) {
          res.status(404).json({
            success: false,
            error: 'Student not found'
          });
          return;
        }
      }

      const subscription = await Subscription.findById(subscriptionId);

      if (!subscription) {
        res.status(404).json({
          success: false,
          error: 'Subscription not found'
        });
        return;
      }

      if (subscription.status !== 'paused') {
        res.status(400).json({
          success: false,
          error: `Cannot resume subscription with status '${subscription.status}'. Only paused subscriptions can be resumed.`
        });
        return;
      }

      // Resume the subscription in Stripe
      if (subscription.stripeSubscriptionId) {
        try {
          await stripe.subscriptions.update(subscription.stripeSubscriptionId, {
            pause_collection: null
          });
        } catch (stripeError: any) {
          console.error('Error resuming subscription in Stripe:', stripeError);
          res.status(400).json({
            success: false,
            error: 'Failed to resume subscription in payment system. Please try again.'
          });
          return;
        }
      }

      // Update local subscription status
      await SubscriptionService.updateSubscriptionStatus(
        subscription._id.toString(),
        { status: 'active' },
        'controller'
      );

      const updatedSubscription = await Subscription.findById(subscription._id)
        .populate('tutorId', 'basePrice teachingSubjects firstname lastname avatar');

      res.json({
        success: true,
        message: 'Subscription resumed successfully',
        data: updatedSubscription
      });
    } catch (error) {
      console.error('Error resuming subscription:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Cancel subscription
  async cancelSubscription(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { subscriptionId } = req.params;

      // Check if user is authenticated
      if (!req.user || !req.user._id) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      // Validate subscription ID format
      if (!subscriptionId || !Types.ObjectId.isValid(subscriptionId)) {
        res.status(400).json({
          success: false,
          error: 'Invalid subscription ID format'
        });
        return;
      }

      // Get student from authenticated user
      let student;
      if (req.user.role === 'student') {
        student = req.user;
      } else {
        // Allow non-students to cancel subscriptions by looking up the student
        const subscription = await Subscription.findById(subscriptionId);
        if (!subscription) {
          res.status(404).json({
            success: false,
            error: 'Subscription not found'
          });
          return;
        }
        student = await Student.findById(subscription.studentId);
        if (!student) {
          res.status(404).json({
            success: false,
            error: 'Student not found'
          });
          return;
        }
      }

      const subscription = await Subscription.findById(subscriptionId);

      if (!subscription) {
        res.status(404).json({
          success: false,
          error: 'Subscription not found'
        });
        return;
      }

      if (subscription.status === 'cancelled') {
        res.status(400).json({
          success: false,
          error: 'Subscription is already cancelled'
        });
        return;
      }

      // Cancel the subscription in Stripe at the end of the current period
      if (subscription.stripeSubscriptionId) {
        try {
          await stripe.subscriptions.update(subscription.stripeSubscriptionId, {
            cancel_at_period_end: true
          });
        } catch (stripeError: any) {
          console.error('Error cancelling subscription in Stripe:', stripeError);
          res.status(400).json({
            success: false,
            error: 'Failed to cancel subscription in payment system. Please try again.'
          });
          return;
        }
      }

      // Update local subscription status
      await SubscriptionService.updateSubscriptionStatus(
        subscription._id.toString(),
        { status: 'cancelled' },
        'controller'
      );

      // Update autoRenew flag
      subscription.autoRenew = false;
      await subscription.save();

      const updatedSubscription = await Subscription.findById(subscription._id)
        .populate('tutorId', 'basePrice teachingSubjects firstname lastname avatar');

      res.json({
        success: true,
        message: 'Subscription cancelled successfully. You can continue using your remaining lessons until the end of the current billing period.',
        data: updatedSubscription
      });
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Switch tutor (requires admin approval)
  async switchTutor(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { subscriptionId } = req.params;
      const { newTutorId } = req.body;

      // Check if user is authenticated
      if (!req.user || !req.user._id) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      // Get student from authenticated user
      let student;
      if (req.user.role === 'student') {
        student = req.user;
      } else {
        // Allow non-students to request tutor transfers by looking up the student
        const subscription = await Subscription.findById(subscriptionId);
        if (!subscription) {
          res.status(404).json({
            success: false,
            error: 'Subscription not found'
          });
          return;
        }
        student = await Student.findById(subscription.studentId);
        if (!student) {
          res.status(404).json({
            success: false,
            error: 'Student not found'
          });
          return;
        }
      }

      const subscription = await Subscription.findById(subscriptionId);

      if (!subscription) {
        res.status(404).json({ error: 'Subscription not found' });
        return;
      }

      // Verify new tutor exists and is approved
      const newTutor = await Tutor.findById(newTutorId);
      if (!newTutor) {
        res.status(404).json({ error: 'New tutor not found' });
        return;
      }

      if (newTutor.approvalStatus !== 'approved') {
        res.status(400).json({ error: 'New tutor is not approved for tutoring' });
        return;
      }

      // Set subscription to pending transfer status
      subscription.status = 'pending_transfer';
      // Store the new tutor ID in metadata for admin approval
      subscription.metadata = {
        ...subscription.metadata,
        pendingTutorId: newTutorId
      };
      
      await subscription.save();

      res.json({
        success: true,
        message: 'Tutor transfer request submitted. Please contact support to complete the process.',
        data: subscription
      });
    } catch (error) {
      console.error('Error switching tutor:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // Get tutor's subscriptions
  async getTutorSubscriptions(req: AuthRequest, res: Response): Promise<void> {
    try {
      // Check if user is authenticated
      if (!req.user || !req.user._id) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      // Get tutor from authenticated user
      let tutor;
      if (req.user.role === 'tutor') {
        tutor = req.user;
      } else {
        // Allow non-tutors to view tutor subscriptions by looking up the tutor
        // This could be for admin purposes or other authorized users
        res.status(403).json({
          success: false,
          error: 'Access denied: Tutor role required to view tutor subscriptions'
        });
        return;
      }

      const subscriptions = await Subscription.find({ tutorId: tutor._id })
        .populate('studentId', 'learningReasons skillsToImprove firstname lastname avatar')
        .sort({ createdAt: -1 });

      res.json({
        success: true,
        data: subscriptions
      });
    } catch (error) {
      console.error('Error fetching tutor subscriptions:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Get all subscriptions (admin only)
  async getAllSubscriptions(req: AuthRequest, res: Response): Promise<void> {
    try {
      // Check if user is authenticated
      if (!req.user || !req.user._id) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      // Role-based restrictions removed - all authenticated users can access

      const { page = 1, limit = 20, status } = req.query;
      const skip = (Number(page) - 1) * Number(limit);

      let query: any = {};
      
      if (status) {
        query.status = status;
      }

      const subscriptions = await Subscription.find(query)
        .populate('studentId', 'learningReasons firstname lastname email')
        .populate('tutorId', 'basePrice teachingSubjects firstname lastname email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(Number(limit));

      const total = await Subscription.countDocuments(query);

      res.json({
        success: true,
        data: subscriptions,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      });
    } catch (error) {
      console.error('Error fetching all subscriptions:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // Approve tutor transfer (admin only)
  async approveTutorTransfer(req: AuthRequest, res: Response): Promise<void> {
    try {
      // Check if user is authenticated
      if (!req.user || !req.user._id) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      // Role-based restrictions removed - all authenticated users can access

      const { subscriptionId } = req.params;
      const { newTutorId } = req.body;

      const subscription = await Subscription.findById(subscriptionId);
      if (!subscription) {
        res.status(404).json({ error: 'Subscription not found' });
        return;
      }

      if (subscription.status !== 'pending_transfer') {
        res.status(400).json({ error: 'Subscription is not pending transfer' });
        return;
      }

      // Verify new tutor exists and is approved
      const newTutor = await Tutor.findById(newTutorId);
      if (!newTutor) {
        res.status(404).json({ error: 'New tutor not found' });
        return;
      }

      if (newTutor.approvalStatus !== 'approved') {
        res.status(400).json({ error: 'New tutor is not approved for tutoring' });
        return;
      }

      subscription.tutorId = Types.ObjectId.createFromHexString(newTutorId);
      subscription.status = 'active';
      
      // Clear the pending tutor from metadata
      if (subscription.metadata) {
        delete subscription.metadata.pendingTutorId;
      }
      
      await subscription.save();

      res.json({
        success: true,
        message: 'Tutor transfer approved successfully',
        data: subscription
      });
    } catch (error) {
      console.error('Error approving tutor transfer:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  // Get subscription by ID
  async getSubscriptionById(req: AuthRequest, res: Response): Promise<void> {
    try {
      // Check if user is authenticated
      if (!req.user || !req.user._id) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      const { subscriptionId } = req.params;

      if (!subscriptionId) {
        res.status(400).json({
          success: false,
          error: 'Subscription ID is required'
        });
        return;
      }

      // Validate ObjectId format
      if (!Types.ObjectId.isValid(subscriptionId)) {
        res.status(400).json({
          success: false,
          error: 'Invalid subscription ID format'
        });
        return;
      }

      const subscription = await Subscription.findById(subscriptionId)
        .populate('studentId', 'learningReasons skillsToImprove firstname lastname email avatar')
        .populate('tutorId', 'basePrice teachingSubjects rating totalLessons firstname lastname avatar');

      if (!subscription) {
        res.status(404).json({
          success: false,
          error: 'Subscription not found'
        });
        return;
      }

      // Allow all authenticated users to view subscription details
      // Role-based restrictions have been removed

      res.json({
        success: true,
        data: subscription
      });
    } catch (error) {
      console.error('Error fetching subscription:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  // Confirm payment after 3D Secure or other authentication
  async confirmPayment(req: AuthRequest, res: Response): Promise<void> {
    try {
      // Check if user is authenticated
      if (!req.user || !req.user._id) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      const { subscriptionId } = req.params;
      const { paymentIntentId } = req.body;

      if (!subscriptionId) {
        res.status(400).json({
          success: false,
          error: 'Subscription ID is required'
        });
        return;
      }

      if (!paymentIntentId) {
        res.status(400).json({
          success: false,
          error: 'Payment Intent ID is required'
        });
        return;
      }

      // Validate ObjectId format
      if (!Types.ObjectId.isValid(subscriptionId)) {
        res.status(400).json({
          success: false,
          error: 'Invalid subscription ID format'
        });
        return;
      }

      const subscription = await Subscription.findById(subscriptionId);
      if (!subscription) {
        res.status(404).json({
          success: false,
          error: 'Subscription not found'
        });
        return;
      }

      // Retrieve payment intent from Stripe
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

      if (paymentIntent.status === 'succeeded') {
        // Update subscription status using service
        await SubscriptionService.updateSubscriptionStatus(
          subscription._id.toString(),
          {
            status: 'active',
            remainingLessons: subscription.lessonsPerWeek * 4
          },
          'controller'
        );

        // Add payment to history using service
        await SubscriptionService.addPaymentToHistory(subscription._id.toString(), {
          amount: paymentIntent.amount / 100,
          currency: paymentIntent.currency,
          status: 'succeeded',
          stripePaymentIntentId: paymentIntent.id,
          stripeInvoiceId: (paymentIntent as any).invoice as string || '',
          description: `Payment confirmation for ${subscription.lessonsPerWeek} lessons weekly subscription`
        });

        // Create confirmation response
        const response = await SubscriptionService.createConfirmationResponse(subscription);
        res.json(response);
      } else {
        res.status(400).json({
          success: false,
          error: `Payment not successful. Status: ${paymentIntent.status}`,
          paymentStatus: paymentIntent.status
        });
      }
    } catch (error: any) {
      console.error('Error confirming payment:', error);
      if (error instanceof Stripe.errors.StripeError) {
        res.status(400).json({
          success: false,
          error: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Internal server error'
        });
      }
    }
  }

  // Handle webhook notifications (internal use)
  async handleWebhookNotification(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { subscriptionId } = req.params;
      const { eventType, eventData, source } = req.body;

      if (!subscriptionId) {
        res.status(400).json({
          success: false,
          error: 'Subscription ID is required'
        });
        return;
      }

      // Validate subscription exists
      await SubscriptionService.validateSubscription(subscriptionId);

      // Log the webhook notification
      console.log(`Webhook notification received for subscription ${subscriptionId}:`, {
        eventType,
        eventData,
        source: source || 'webhook'
      });

      // Handle different event types
      switch (eventType) {
        case 'invoice.payment_succeeded':
          console.log(`Payment succeeded for subscription ${subscriptionId}`);
          break;

        case 'invoice.payment_failed':
          console.log(`Payment failed for subscription ${subscriptionId}`);
          break;

        case 'customer.subscription.updated':
          console.log(`Subscription ${subscriptionId} updated via Stripe`);
          break;

        case 'customer.subscription.deleted':
          console.log(`Subscription ${subscriptionId} cancelled via Stripe`);
          break;

        case 'payment_intent.succeeded':
          console.log(`Payment intent succeeded for subscription ${subscriptionId}`);
          break;

        default:
          console.log(`Unhandled webhook event type: ${eventType}`);
      }

      // You can add additional logic here to:
      // - Send notifications to users
      // - Update UI in real-time
      // - Trigger other business logic
      // - Log analytics events

      res.json({
        success: true,
        message: 'Webhook notification processed',
        data: {
          subscriptionId,
          eventType,
          processedAt: new Date()
        }
      });
    } catch (error) {
      console.error('Error handling webhook notification:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
}