import { Response } from 'express';
import { AuthRequest } from '../types/AuthRequest';
import { CardTokenizationService, CardDetails } from '../services/cardTokenizationService';
import { createErrorResponse } from '../middlewares/errorHandler';
import Student from '../models/student';
import <PERSON><PERSON> from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: '2025-05-28.basil',
});

/**
 * Tokenize card details for secure storage and future use
 */
export const tokenizeCard = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    const {
      number,
      exp_month,
      exp_year,
      cvc,
      name,
      address_line1,
      address_line2,
      address_city,
      address_state,
      address_zip,
      address_country
    } = req.body;

    // Validate required fields
    if (!number || !exp_month || !exp_year || !cvc) {
      createErrorResponse(res, 'Missing required card details: number, exp_month, exp_year, cvc', 400);
      return;
    }

    const cardDetails: CardDetails = {
      number: number.replace(/\s/g, ''), // Remove spaces
      exp_month: parseInt(exp_month),
      exp_year: parseInt(exp_year),
      cvc: cvc.toString(),
      name: name || `${req.user.firstname} ${req.user.lastname}`,
      address_line1,
      address_line2,
      address_city,
      address_state,
      address_zip,
      address_country: address_country || 'US'
    };

    // Get or create Stripe customer
    let stripeCustomerId: string | undefined;
    if (req.user.role === 'student') {
      const student = await Student.findById(req.user._id);
      if (student?.stripeCustomerId) {
        stripeCustomerId = student.stripeCustomerId;
      } else {
        // Create Stripe customer
        const stripeCustomer = await stripe.customers.create({
          email: req.user.email,
          name: `${req.user.firstname} ${req.user.lastname}`,
          metadata: {
            userId: req.user._id.toString(),
            userType: 'student'
          }
        });
        
        stripeCustomerId = stripeCustomer.id;
        
        // Update student record
        if (student) {
          student.stripeCustomerId = stripeCustomerId;
          await student.save();
        }
      }
    }

    // Tokenize the card
    const result = await CardTokenizationService.tokenizeCard(cardDetails, stripeCustomerId);

    if (!result.success) {
      createErrorResponse(res, result.error || 'Failed to tokenize card', 400);
      return;
    }

    // Return tokenized card info (without sensitive data)
    res.status(201).json({
      success: true,
      message: 'Card tokenized successfully',
      data: {
        token: result.tokenizedCard?.token,
        paymentMethodId: result.paymentMethodId,
        card: {
          last4: result.tokenizedCard?.last4,
          brand: result.tokenizedCard?.brand,
          exp_month: result.tokenizedCard?.exp_month,
          exp_year: result.tokenizedCard?.exp_year
        },
        expiresAt: result.tokenizedCard?.expiresAt
      }
    });

  } catch (error: any) {
    console.error('Error tokenizing card:', error);
    
    if (error.type === 'StripeCardError') {
      createErrorResponse(res, error.message || 'Card was declined', 400);
    } else if (error.type === 'StripeInvalidRequestError') {
      createErrorResponse(res, 'Invalid card details provided', 400);
    } else {
      createErrorResponse(res, 'Failed to tokenize card', 500);
    }
  }
};

/**
 * Validate card details without tokenizing
 */
export const validateCard = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    const { number, exp_month, exp_year, cvc } = req.body;

    if (!number || !exp_month || !exp_year || !cvc) {
      createErrorResponse(res, 'Missing required card details', 400);
      return;
    }

    const cardDetails: CardDetails = {
      number: number.replace(/\s/g, ''),
      exp_month: parseInt(exp_month),
      exp_year: parseInt(exp_year),
      cvc: cvc.toString()
    };

    // Use private method through a test tokenization (without storing)
    try {
      const testResult = await CardTokenizationService.tokenizeCard(cardDetails);
      
      if (testResult.success && testResult.paymentMethodId) {
        // Clean up test payment method
        await stripe.paymentMethods.detach(testResult.paymentMethodId);
      }

      res.json({
        success: true,
        message: 'Card details are valid',
        data: {
          valid: testResult.success,
          brand: testResult.tokenizedCard?.brand,
          last4: testResult.tokenizedCard?.last4
        }
      });

    } catch (validationError: any) {
      res.json({
        success: false,
        message: 'Card validation failed',
        data: {
          valid: false,
          error: validationError.message
        }
      });
    }

  } catch (error) {
    console.error('Error validating card:', error);
    createErrorResponse(res, 'Failed to validate card', 500);
  }
};

/**
 * Get tokenized card information
 */
export const getTokenizedCard = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    const { token } = req.params;

    if (!token) {
      createErrorResponse(res, 'Token is required', 400);
      return;
    }

    const result = await CardTokenizationService.createPaymentMethodFromToken(token);

    if (!result.success) {
      createErrorResponse(res, result.error || 'Invalid token', 400);
      return;
    }

    res.json({
      success: true,
      data: {
        token: result.tokenizedCard?.token,
        paymentMethodId: result.paymentMethodId,
        card: {
          last4: result.tokenizedCard?.last4,
          brand: result.tokenizedCard?.brand,
          exp_month: result.tokenizedCard?.exp_month,
          exp_year: result.tokenizedCard?.exp_year
        },
        created: result.tokenizedCard?.created,
        expiresAt: result.tokenizedCard?.expiresAt
      }
    });

  } catch (error) {
    console.error('Error getting tokenized card:', error);
    createErrorResponse(res, 'Failed to retrieve tokenized card', 500);
  }
};

/**
 * List user's saved payment methods
 */
export const getPaymentMethods = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    if (req.user.role !== 'student') {
      createErrorResponse(res, 'Only students can view payment methods', 403);
      return;
    }

    const student = await Student.findById(req.user._id);
    if (!student?.stripeCustomerId) {
      res.json({
        success: true,
        data: {
          paymentMethods: []
        }
      });
      return;
    }

    const paymentMethods = await stripe.paymentMethods.list({
      customer: student.stripeCustomerId,
      type: 'card',
    });

    const formattedMethods = paymentMethods.data.map(pm => ({
      id: pm.id,
      card: {
        brand: pm.card?.brand,
        last4: pm.card?.last4,
        exp_month: pm.card?.exp_month,
        exp_year: pm.card?.exp_year
      },
      created: new Date(pm.created * 1000)
    }));

    res.json({
      success: true,
      data: {
        paymentMethods: formattedMethods
      }
    });

  } catch (error) {
    console.error('Error getting payment methods:', error);
    createErrorResponse(res, 'Failed to retrieve payment methods', 500);
  }
};

/**
 * Delete a payment method
 */
export const deletePaymentMethod = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    const { paymentMethodId } = req.params;

    if (!paymentMethodId) {
      createErrorResponse(res, 'Payment method ID is required', 400);
      return;
    }

    // Verify the payment method belongs to the user
    const paymentMethod = await stripe.paymentMethods.retrieve(paymentMethodId);
    
    if (req.user.role === 'student') {
      const student = await Student.findById(req.user._id);
      if (paymentMethod.customer !== student?.stripeCustomerId) {
        createErrorResponse(res, 'Payment method not found', 404);
        return;
      }
    }

    // Detach the payment method
    await stripe.paymentMethods.detach(paymentMethodId);

    res.json({
      success: true,
      message: 'Payment method deleted successfully'
    });

  } catch (error: any) {
    console.error('Error deleting payment method:', error);
    
    if (error.type === 'StripeInvalidRequestError') {
      createErrorResponse(res, 'Payment method not found', 404);
    } else {
      createErrorResponse(res, 'Failed to delete payment method', 500);
    }
  }
};
