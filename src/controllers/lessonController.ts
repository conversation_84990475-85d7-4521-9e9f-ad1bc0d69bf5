import { Response } from 'express';
import { Types } from 'mongoose';
import { Lesson } from '../models/Lesson';
import { Calendar } from '../models/calendar';
import { Schedule } from '../models/Schedule';
import Subscription from '../models/subscription.model';
import Student from '../models/student';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';
import { autoProcessEarnings } from './earningsController';

/**
 * Schedule a lesson (Student schedules with <PERSON><PERSON>)
 */
export const scheduleLesson = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'student') {
      createErrorResponse(res, 'Only students can schedule lessons', 403);
      return;
    }

    const {
      tutorId,
      tutorCalendarId,
      startDateTime,
      endDateTime,
      subject,
      description,
      lessonType = 'regular',
      timezone = 'UTC'
    } = req.body;

    // Validate required fields
    if (!tutorId || !tutorCalendarId || !startDateTime || !endDateTime) {
      createErrorResponse(res, 'Missing required fields: tutorId, tutorCalendarId, startDateTime, endDateTime', 400);
      return;
    }

    // Validate dates
    const start = new Date(startDateTime);
    const end = new Date(endDateTime);
    const now = new Date();

    if (start <= now) {
      createErrorResponse(res, 'Lesson cannot be scheduled in the past', 400);
      return;
    }

    if (end <= start) {
      createErrorResponse(res, 'End time must be after start time', 400);
      return;
    }

    const duration = Math.floor((end.getTime() - start.getTime()) / (1000 * 60));
    if (duration < 15) {
      createErrorResponse(res, 'Lesson must be at least 15 minutes long', 400);
      return;
    }

    // Check if tutor calendar exists and is active
    const tutorCalendar = await Calendar.findOne({
      _id: tutorCalendarId,
      ownerId: tutorId,
      ownerType: 'tutor',
      isActive: true
    });

    if (!tutorCalendar) {
      createErrorResponse(res, 'Tutor calendar not found or inactive', 404);
      return;
    }

    // Check for time slot conflicts
    const conflictingLesson = await Lesson.findOne({
      tutorId: tutorId,
      status: { $in: ['scheduled', 'confirmed', 'in_progress'] },
      $or: [
        {
          startDateTime: { $lt: end },
          endDateTime: { $gt: start }
        }
      ]
    });

    const hasConflict = !!conflictingLesson;

    if (hasConflict) {
      createErrorResponse(res, 'Time slot conflicts with existing lesson', 409);
      return;
    }

    // Check subscription or trial eligibility
    let subscription = null;
    let isTrialLesson = false;

    if (lessonType === 'trial') {
      // Check trial eligibility
      const student = await Student.findById(req.user._id);
      if (student?.hasUsedFreeTrial) {
        createErrorResponse(res, 'Free trial already used', 403);
        return;
      }
      isTrialLesson = true;
    } else {
      // Check for active subscription
      subscription = await Subscription.findOne({
        studentId: req.user._id,
        tutorId: tutorId,
        status: 'active'
      });

      if (!subscription) {
        createErrorResponse(res, 'Active subscription required to schedule regular lessons', 403);
        return;
      }

      if (!subscription.canScheduleLesson()) {
        createErrorResponse(res, 'Subscription does not allow scheduling lessons at this time', 403);
        return;
      }

      if (subscription.remainingLessons <= 0) {
        createErrorResponse(res, 'No remaining lessons in current subscription period', 403);
        return;
      }
    }

    // Get or create student calendar
    let studentCalendar = await Calendar.findOne({
      ownerId: req.user._id,
      ownerType: 'student',
      isActive: true
    });

    if (!studentCalendar) {
      studentCalendar = new Calendar({
        ownerId: req.user._id,
        ownerType: 'student',
        name: 'My Lessons',
        description: 'My scheduled lessons and classes',
        color: '#10B981', // Green color for student
        isShared: false,
        timezone: timezone,
        isActive: true
      });
      await studentCalendar.save();
    }

    // Create the lesson
    const lesson = new Lesson({
      studentId: req.user._id,
      tutorId: tutorId,
      tutorCalendarId: tutorCalendarId,
      studentCalendarId: studentCalendar._id,
      subscriptionId: subscription?._id,
      title: `${lessonType === 'trial' ? 'Trial ' : ''}Lesson${subject ? ` - ${subject}` : ''}`,
      description,
      subject,
      lessonType,
      startDateTime: start,
      endDateTime: end,
      duration,
      timezone,
      status: 'scheduled',
      isTrialLesson,
      trialUsed: isTrialLesson,
      price: isTrialLesson ? 0 : subscription?.monthlyPrice || 0,
      paymentStatus: isTrialLesson ? 'free_trial' : 'pending',
      metadata: {
        createdBy: 'student',
        source: 'web_app'
      }
    });

    await lesson.save();

    // Log student booking activity
    console.log(`📅 Student Booking Activity:`, {
      studentId: req.user._id,
      studentName: `${(req.user as any).firstname || 'Unknown'} ${(req.user as any).lastname || 'Student'}`,
      tutorId: tutorId,
      tutorCalendarId: tutorCalendarId,
      lessonId: lesson._id,
      lessonType: lessonType,
      startDateTime: start,
      duration: duration,
      subject: subject,
      price: lesson.price,
      isTrialLesson: isTrialLesson,
      subscriptionId: subscription?._id,
      bookedAt: new Date(),
      source: 'web_app'
    });

    // Update subscription if not trial
    if (subscription && !isTrialLesson) {
      subscription.remainingLessons -= 1;
      await subscription.save();

      console.log(`📊 Subscription Updated:`, {
        subscriptionId: subscription._id,
        remainingLessons: subscription.remainingLessons,
        studentId: req.user._id,
        tutorId: tutorId
      });
    }

    // Mark trial as used if trial lesson
    if (isTrialLesson) {
      await Student.findByIdAndUpdate(req.user._id, { hasUsedFreeTrial: true });

      console.log(`🎯 Trial Lesson Booked:`, {
        studentId: req.user._id,
        tutorId: tutorId,
        lessonId: lesson._id,
        trialUsed: true
      });
    }

    // Populate lesson data for response
    const populatedLesson = await Lesson.findById(lesson._id)
      .populate('tutorId', 'firstname lastname email avatar')
      .populate('studentId', 'firstname lastname email avatar')
      .populate('subscriptionId', 'planType remainingLessons');

    res.status(201).json({
      success: true,
      message: 'Lesson scheduled successfully',
      data: populatedLesson
    });

  } catch (error) {
    console.error('Error scheduling lesson:', error);
    createErrorResponse(res, 'Failed to schedule lesson', 500);
  }
};

/**
 * Get lessons for a user (student or tutor)
 */
export const getMyLessons = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    const { 
      status, 
      startDate, 
      endDate, 
      limit = 50, 
      page = 1,
      upcoming = false 
    } = req.query;

    let query: any = {};
    
    // Set user-specific query
    if (req.user.role === 'student') {
      query.studentId = req.user._id;
    } else if (req.user.role === 'tutor') {
      query.tutorId = req.user._id;
    } else {
      createErrorResponse(res, 'Invalid user role', 403);
      return;
    }

    // Filter by status
    if (status) {
      query.status = status;
    }

    // Filter by date range
    if (startDate || endDate) {
      query.startDateTime = {};
      if (startDate) query.startDateTime.$gte = new Date(startDate as string);
      if (endDate) query.startDateTime.$lte = new Date(endDate as string);
    }

    // Show only upcoming lessons
    if (upcoming === 'true') {
      query.startDateTime = { $gte: new Date() };
      query.status = { $in: ['scheduled', 'confirmed'] };
    }

    const skip = (Number(page) - 1) * Number(limit);

    const [lessons, totalCount] = await Promise.all([
      Lesson.find(query)
        .populate('studentId', 'firstname lastname email avatar')
        .populate('tutorId', 'firstname lastname email avatar')
        .populate('subscriptionId', 'planType remainingLessons')
        .sort({ startDateTime: upcoming === 'true' ? 1 : -1 })
        .skip(skip)
        .limit(Number(limit)),
      Lesson.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: lessons,
      pagination: {
        currentPage: Number(page),
        totalPages: Math.ceil(totalCount / Number(limit)),
        totalCount,
        hasNext: skip + lessons.length < totalCount,
        hasPrev: Number(page) > 1
      }
    });

  } catch (error) {
    console.error('Error fetching lessons:', error);
    createErrorResponse(res, 'Failed to fetch lessons', 500);
  }
};

/**
 * Get tutor's schedule (only if student has subscription or trial eligibility)
 */
export const getTutorSchedule = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'student') {
      createErrorResponse(res, 'Only students can view tutor schedules', 403);
      return;
    }

    const { tutorId } = req.params;
    const { startDate, endDate } = req.query;

    if (!tutorId || !Types.ObjectId.isValid(tutorId)) {
      createErrorResponse(res, 'Valid tutor ID required', 400);
      return;
    }

    // Check if student has subscription or trial eligibility with this tutor
    const [subscription, student] = await Promise.all([
      Subscription.findOne({
        studentId: req.user._id,
        tutorId: tutorId,
        status: 'active'
      }),
      Student.findById(req.user._id)
    ]);

    const hasSubscription = !!subscription;
    const canUseTrial = !student?.hasUsedFreeTrial;

    if (!hasSubscription && !canUseTrial) {
      createErrorResponse(res, 'You need an active subscription or trial eligibility to view this tutor\'s schedule', 403);
      return;
    }

    // Get tutor's calendar
    const tutorCalendar = await Calendar.findOne({
      ownerId: tutorId,
      ownerType: 'tutor',
      isActive: true,
      isShared: true
    }).populate('defaultScheduleId');

    if (!tutorCalendar) {
      createErrorResponse(res, 'Tutor calendar not found or not shared', 404);
      return;
    }

    // Get date range
    const start = startDate ? new Date(startDate as string) : new Date();
    const end = endDate ? new Date(endDate as string) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days ahead

    // Get existing lessons in the date range
    const existingLessons = await Lesson.find({
      tutorId: tutorId,
      startDateTime: { $gte: start, $lte: end },
      status: { $in: ['scheduled', 'confirmed', 'in_progress'] }
    }).select('startDateTime endDateTime status');

    // Get tutor's schedule if available
    let scheduleData = null;
    if (tutorCalendar.hasSchedule && tutorCalendar.defaultScheduleId) {
      scheduleData = await Schedule.findById(tutorCalendar.defaultScheduleId);
    }

    res.json({
      success: true,
      data: {
        calendar: {
          id: tutorCalendar._id,
          name: tutorCalendar.name,
          color: tutorCalendar.color,
          timezone: tutorCalendar.timezone
        },
        schedule: scheduleData,
        existingLessons,
        accessInfo: {
          hasSubscription,
          canUseTrial,
          remainingLessons: subscription?.remainingLessons || 0
        },
        dateRange: { start, end }
      }
    });

  } catch (error) {
    console.error('Error fetching tutor schedule:', error);
    createErrorResponse(res, 'Failed to fetch tutor schedule', 500);
  }
};

/**
 * Cancel a lesson
 */
export const cancelLesson = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    const { lessonId } = req.params;
    const { reason } = req.body;

    if (!lessonId || !Types.ObjectId.isValid(lessonId)) {
      createErrorResponse(res, 'Valid lesson ID required', 400);
      return;
    }

    const lesson = await Lesson.findById(lessonId);
    if (!lesson) {
      createErrorResponse(res, 'Lesson not found', 404);
      return;
    }

    // Check if user is authorized to cancel
    const isStudent = req.user.role === 'student' && lesson.studentId.toString() === (req.user._id as Types.ObjectId).toString();
    const isTutor = req.user.role === 'tutor' && lesson.tutorId.toString() === (req.user._id as Types.ObjectId).toString();

    if (!isStudent && !isTutor) {
      createErrorResponse(res, 'Not authorized to cancel this lesson', 403);
      return;
    }

    // Check if lesson can be cancelled (24 hours notice)
    const now = new Date();
    const lessonStart = new Date(lesson.startDateTime);
    const hoursUntilLesson = (lessonStart.getTime() - now.getTime()) / (1000 * 60 * 60);

    if (lesson.status !== 'scheduled' || hoursUntilLesson < 24) {
      createErrorResponse(res, 'Lesson cannot be cancelled (less than 24 hours notice or already started)', 400);
      return;
    }

    // Cancel the lesson
    lesson.status = 'cancelled';
    if (lesson.metadata) {
      lesson.metadata.cancelledBy = req.user.role === 'admin' ? 'system' : req.user.role;
      lesson.metadata.cancelledAt = new Date();
      lesson.metadata.cancellationReason = reason;
    } else {
      lesson.metadata = {
        createdBy: 'system',
        cancelledBy: req.user.role === 'admin' ? 'system' : req.user.role,
        cancelledAt: new Date(),
        cancellationReason: reason
      };
    }

    await lesson.save();

    // Restore subscription lesson if it was a paid lesson
    if (lesson.subscriptionId && !lesson.isTrialLesson) {
      await Subscription.findByIdAndUpdate(
        lesson.subscriptionId,
        { $inc: { remainingLessons: 1 } }
      );
    }

    res.json({
      success: true,
      message: 'Lesson cancelled successfully',
      data: lesson
    });

  } catch (error) {
    console.error('Error cancelling lesson:', error);
    createErrorResponse(res, 'Failed to cancel lesson', 500);
  }
};

/**
 * Complete a lesson (mark as completed)
 */
export const completeLesson = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can mark lessons as completed', 403);
      return;
    }

    const { lessonId } = req.params;
    const { studentAttended, tutorNotes, homework } = req.body;

    if (!lessonId || !Types.ObjectId.isValid(lessonId)) {
      createErrorResponse(res, 'Valid lesson ID required', 400);
      return;
    }

    const lesson = await Lesson.findById(lessonId);
    if (!lesson) {
      createErrorResponse(res, 'Lesson not found', 404);
      return;
    }

    if (lesson.tutorId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'Not authorized to complete this lesson', 403);
      return;
    }

    if (lesson.status !== 'scheduled' && lesson.status !== 'confirmed' && lesson.status !== 'in_progress') {
      createErrorResponse(res, 'Lesson cannot be completed in current status', 400);
      return;
    }

    // Mark as completed
    lesson.status = 'completed';
    lesson.completedAt = new Date();
    lesson.studentAttended = studentAttended !== false; // Default to true if not specified
    lesson.tutorAttended = true;

    // Update lesson notes
    if (tutorNotes) lesson.tutorNotes = tutorNotes;
    if (homework) lesson.homework = homework;

    await lesson.save();

    // Process earnings automatically after lesson completion
    try {
      await autoProcessEarnings(lesson._id as Types.ObjectId);
    } catch (earningsError) {
      console.error('Failed to process earnings for completed lesson:', earningsError);
      // Don't fail the lesson completion if earnings processing fails
    }

    const populatedLesson = await Lesson.findById(lesson._id)
      .populate('studentId', 'firstname lastname email avatar')
      .populate('tutorId', 'firstname lastname email avatar');

    res.json({
      success: true,
      message: 'Lesson completed successfully',
      data: populatedLesson
    });

  } catch (error) {
    console.error('Error completing lesson:', error);
    createErrorResponse(res, 'Failed to complete lesson', 500);
  }
};

/**
 * Get student booking history for a tutor (Tutor can see which students book lessons)
 */
export const getStudentBookingHistory = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can view student booking history', 403);
      return;
    }

    const { startDate, endDate, limit = 50, studentId } = req.query;

    // Build query for lessons with this tutor
    const query: any = {
      tutorId: req.user._id,
      status: { $in: ['scheduled', 'confirmed', 'completed', 'cancelled', 'in_progress'] }
    };

    // Filter by specific student if provided
    if (studentId && Types.ObjectId.isValid(studentId as string)) {
      query.studentId = studentId;
    }

    // Filter by date range
    if (startDate || endDate) {
      query.startDateTime = {};
      if (startDate) query.startDateTime.$gte = new Date(startDate as string);
      if (endDate) query.startDateTime.$lte = new Date(endDate as string);
    }

    const lessons = await Lesson.find(query)
      .populate('studentId', 'firstname lastname email avatar')
      .populate('subscriptionId', 'planType lessonsPerWeek')
      .sort({ startDateTime: -1 })
      .limit(parseInt(limit as string));

    // Group by student and calculate statistics
    const studentStats: { [key: string]: any } = {};
    let totalRevenue = 0;

    lessons.forEach(lesson => {
      const studentId = (lesson.studentId as any)._id.toString();
      const studentInfo = lesson.studentId as any;

      if (!studentStats[studentId]) {
        studentStats[studentId] = {
          student: {
            id: studentId,
            name: `${studentInfo.firstname} ${studentInfo.lastname}`,
            email: studentInfo.email,
            avatar: studentInfo.avatar
          },
          totalBookings: 0,
          completedLessons: 0,
          upcomingLessons: 0,
          cancelledLessons: 0,
          trialLessons: 0,
          totalRevenue: 0,
          totalHours: 0,
          averageRating: 0,
          firstBooking: null,
          lastBooking: null,
          subscriptionType: null,
          recentLessons: []
        };
      }

      const stats = studentStats[studentId];
      stats.totalBookings++;
      stats.totalRevenue += lesson.price || 0;
      stats.totalHours += (lesson.duration || 0) / 60;
      totalRevenue += lesson.price || 0;

      // Count by status
      if (lesson.status === 'completed') stats.completedLessons++;
      else if (lesson.status === 'cancelled') stats.cancelledLessons++;
      else if (['scheduled', 'confirmed', 'in_progress'].includes(lesson.status)) stats.upcomingLessons++;

      // Count trial lessons
      if (lesson.isTrialLesson) stats.trialLessons++;

      // Track subscription type
      if (lesson.subscriptionId) {
        stats.subscriptionType = (lesson.subscriptionId as any).planType;
      }

      // Update booking dates
      const lessonDate = new Date(lesson.startDateTime);
      if (!stats.firstBooking || lessonDate < new Date(stats.firstBooking)) {
        stats.firstBooking = lesson.startDateTime;
      }
      if (!stats.lastBooking || lessonDate > new Date(stats.lastBooking)) {
        stats.lastBooking = lesson.startDateTime;
      }

      // Add to recent lessons (limit to 5 per student)
      if (stats.recentLessons.length < 5) {
        stats.recentLessons.push({
          id: lesson._id,
          title: lesson.title,
          subject: lesson.subject,
          startDateTime: lesson.startDateTime,
          duration: lesson.duration,
          status: lesson.status,
          price: lesson.price,
          isTrialLesson: lesson.isTrialLesson,
          bookedAt: lesson.createdAt
        });
      }
    });

    // Convert to array and sort by total bookings
    const studentsArray = Object.values(studentStats).sort((a: any, b: any) => b.totalBookings - a.totalBookings);

    // Calculate overall statistics
    const overallStats = {
      totalStudents: studentsArray.length,
      totalBookings: lessons.length,
      totalRevenue,
      totalHours: lessons.reduce((sum, lesson) => sum + ((lesson.duration || 0) / 60), 0),
      averageRevenuePerStudent: studentsArray.length > 0 ? Math.round(totalRevenue / studentsArray.length) : 0,
      averageBookingsPerStudent: studentsArray.length > 0 ? Math.round(lessons.length / studentsArray.length) : 0,
      completionRate: lessons.length > 0 ? Math.round((lessons.filter(l => l.status === 'completed').length / lessons.length) * 100) : 0,
      trialConversionRate: lessons.length > 0 ? Math.round((lessons.filter(l => !l.isTrialLesson).length / lessons.length) * 100) : 0
    };

    res.json({
      success: true,
      data: {
        overallStats,
        students: studentsArray,
        totalStudents: studentsArray.length,
        dateRange: {
          startDate: startDate || null,
          endDate: endDate || null
        }
      }
    });

  } catch (error) {
    console.error('Error fetching student booking history:', error);
    createErrorResponse(res, 'Failed to fetch student booking history', 500);
  }
};
