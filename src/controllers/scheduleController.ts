import { Response } from 'express';
import { Types } from 'mongoose';
import { Schedule, ISchedule, IDaySchedule, ITimeSlot, IScheduleException } from '../models/Schedule';
import { Calendar } from '../models/calendar';
import { Lesson } from '../models/Lesson';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';

/**
 * Create a new schedule for a tutor
 */
export const createSchedule = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can create schedules', 403);
      return;
    }

    if (!req.user._id) {
      createErrorResponse(res, 'User ID not found in request', 401);
      return;
    }

    const {
      calendarId,
      name,
      description,
      timezone = 'UTC',
      scheduleType = 'weekly',
      weeklySchedule,
      dailySchedule,
      monthlySchedule,
      customSchedule,
      flexibleSchedule,
      settings,
      isDefault = false
    } = req.body;

    // Validate required fields
    if (!calendarId || !name) {
      createErrorResponse(res, 'calendarId and name are required', 400);
      return;
    }

    if (!Types.ObjectId.isValid(calendarId)) {
      createErrorResponse(res, 'Invalid calendar ID format', 400);
      return;
    }

    // Verify calendar ownership
    const calendar = await Calendar.findById(calendarId);
    if (!calendar) {
      createErrorResponse(res, 'Calendar not found', 404);
      return;
    }

    if (!calendar.ownerId) {
      createErrorResponse(res, 'Invalid calendar or user data', 400);
      return;
    }

    if (calendar.ownerId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'You do not own this calendar', 403);
      return;
    }

    // Validate schedule data based on schedule type
    switch (scheduleType) {
      case 'weekly':
        if (!weeklySchedule || !Array.isArray(weeklySchedule) || weeklySchedule.length === 0) {
          createErrorResponse(res, 'weeklySchedule is required for weekly schedule type', 400);
          return;
        }
        break;

      case 'daily':
        if (!dailySchedule || !dailySchedule.timeSlots || dailySchedule.timeSlots.length === 0) {
          createErrorResponse(res, 'dailySchedule with timeSlots is required for daily schedule type', 400);
          return;
        }
        break;

      case 'monthly':
        if (!monthlySchedule || !Array.isArray(monthlySchedule) || monthlySchedule.length === 0) {
          createErrorResponse(res, 'monthlySchedule is required for monthly schedule type', 400);
          return;
        }
        break;

      case 'custom':
        if (!customSchedule || !customSchedule.specificDates || customSchedule.specificDates.length === 0) {
          createErrorResponse(res, 'customSchedule with specificDates is required for custom schedule type', 400);
          return;
        }
        break;

      case 'flexible':
        if (!flexibleSchedule || !flexibleSchedule.availableHoursPerWeek) {
          createErrorResponse(res, 'flexibleSchedule with availableHoursPerWeek is required for flexible schedule type', 400);
          return;
        }
        break;

      default:
        createErrorResponse(res, 'Invalid schedule type. Must be one of: weekly, daily, monthly, custom, flexible', 400);
        return;
    }

    // Create the schedule
    const scheduleData: any = {
      tutorId: req.user._id,
      calendarId: calendarId,
      name,
      description,
      timezone,
      scheduleType,
      settings: {
        autoGenerateEvents: true,
        advanceGenerationDays: 30,
        defaultSessionDuration: 60,
        bufferTimeBetweenSessions: 15,
        allowBackToBackBookings: false,
        ...settings
      },
      isDefault,
      exceptions: []
    };

    // Add schedule pattern based on type
    switch (scheduleType) {
      case 'weekly':
        scheduleData.weeklySchedule = weeklySchedule;
        break;
      case 'daily':
        scheduleData.dailySchedule = dailySchedule;
        break;
      case 'monthly':
        scheduleData.monthlySchedule = monthlySchedule;
        break;
      case 'custom':
        scheduleData.customSchedule = customSchedule;
        break;
      case 'flexible':
        scheduleData.flexibleSchedule = flexibleSchedule;
        break;
    }

    const schedule = new Schedule(scheduleData);

    await schedule.save();

    // If this is set as default, update the calendar
    if (isDefault) {
      await Calendar.findByIdAndUpdate(calendarId, {
        hasSchedule: true,
        defaultScheduleId: schedule._id
      });
    }

    const populatedSchedule = await Schedule.findById(schedule._id)
      .populate('calendarId', 'name color')
      .populate('tutorId', 'firstname lastname email');

    res.status(201).json({
      success: true,
      message: 'Schedule created successfully',
      data: populatedSchedule
    });

  } catch (error) {
    console.error('Error creating schedule:', error);
    createErrorResponse(res, 'Failed to create schedule', 500);
  }
};

/**
 * Get tutor's schedules
 */
export const getTutorSchedules = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can view their schedules', 403);
      return;
    }

    const { calendarId, includeInactive = false } = req.query;

    // Build query
    const query: any = { tutorId: req.user._id };
    
    if (calendarId && Types.ObjectId.isValid(calendarId as string)) {
      query.calendarId = new Types.ObjectId(calendarId as string);
    }

    if (includeInactive !== 'true') {
      query.isActive = true;
    }

    const schedules = await Schedule.find(query)
      .populate('calendarId', 'name color isActive')
      .sort({ isDefault: -1, createdAt: -1 });

    // Add summary information for each schedule
    const schedulesWithSummary = schedules.map(schedule => {
      let workingDays = 0;
      let totalSlots = 0;

      // Calculate summary based on schedule type
      switch (schedule.scheduleType) {
        case 'weekly':
          workingDays = schedule.weeklySchedule.filter((day: IDaySchedule) => day.isWorkingDay).length;
          totalSlots = schedule.weeklySchedule.reduce((total: number, day: IDaySchedule) =>
            total + day.timeSlots.filter((slot: ITimeSlot) => slot.isAvailable && slot.sessionType !== 'break').length, 0
          );
          break;

        case 'daily':
          workingDays = 7; // Potentially every day
          totalSlots = schedule.dailySchedule?.timeSlots?.filter((slot: ITimeSlot) =>
            slot.isAvailable && slot.sessionType !== 'break'
          ).length || 0;
          totalSlots *= 7; // Multiply by 7 days
          break;

        case 'monthly':
          workingDays = schedule.monthlySchedule?.length || 0;
          totalSlots = schedule.monthlySchedule?.reduce((total: number, pattern: any) =>
            total + pattern.timeSlots.filter((slot: ITimeSlot) => slot.isAvailable && slot.sessionType !== 'break').length, 0
          ) || 0;
          break;

        case 'custom':
          workingDays = schedule.customSchedule?.specificDates?.length || 0;
          totalSlots = schedule.customSchedule?.specificDates?.reduce((total: number, customDay: any) =>
            total + customDay.timeSlots.filter((slot: ITimeSlot) => slot.isAvailable && slot.sessionType !== 'break').length, 0
          ) || 0;
          break;

        case 'flexible':
          workingDays = 7; // Flexible, potentially any day
          totalSlots = schedule.flexibleSchedule?.preferredTimeSlots?.filter((slot: ITimeSlot) =>
            slot.isAvailable && slot.sessionType !== 'break'
          ).length || 0;
          break;
      }

      return {
        ...schedule.toObject(),
        summary: {
          scheduleType: schedule.scheduleType,
          workingDaysPerWeek: workingDays,
          totalAvailableSlots: totalSlots,
          averageSlotsPerDay: workingDays > 0 ? Math.round(totalSlots / workingDays) : 0,
          upcomingExceptions: schedule.exceptions.filter((exc: IScheduleException) =>
            exc.date > new Date()
          ).length,
          isFlexible: schedule.scheduleType === 'flexible',
          availableHoursPerWeek: schedule.scheduleType === 'flexible' ? schedule.flexibleSchedule?.availableHoursPerWeek : null
        }
      };
    });

    res.json({
      success: true,
      data: schedulesWithSummary
    });

  } catch (error) {
    console.error('Error fetching tutor schedules:', error);
    createErrorResponse(res, 'Failed to fetch schedules', 500);
  }
};

/**
 * Get schedule by ID
 */
export const getScheduleById = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can view schedules', 403);
      return;
    }

    const { scheduleId } = req.params;

    if (!Types.ObjectId.isValid(scheduleId)) {
      createErrorResponse(res, 'Invalid schedule ID format', 400);
      return;
    }

    const schedule = await Schedule.findById(scheduleId)
      .populate('calendarId', 'name color timezone')
      .populate('tutorId', 'firstname lastname email');

    if (!schedule) {
      createErrorResponse(res, 'Schedule not found', 404);
      return;
    }

    // Verify ownership
    if (schedule.tutorId._id.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'You do not own this schedule', 403);
      return;
    }

    res.json({
      success: true,
      data: schedule
    });

  } catch (error) {
    console.error('Error fetching schedule:', error);
    createErrorResponse(res, 'Failed to fetch schedule', 500);
  }
};

/**
 * Update schedule
 */
export const updateSchedule = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can update schedules', 403);
      return;
    }

    const { scheduleId } = req.params;
    const updateData = req.body;

    if (!Types.ObjectId.isValid(scheduleId)) {
      createErrorResponse(res, 'Invalid schedule ID format', 400);
      return;
    }

    const schedule = await Schedule.findById(scheduleId);
    if (!schedule) {
      createErrorResponse(res, 'Schedule not found', 404);
      return;
    }

    // Verify ownership
    if (schedule.tutorId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'You do not own this schedule', 403);
      return;
    }

    // Update allowed fields
    const allowedFields = [
      'name', 'description', 'timezone', 'scheduleType',
      'weeklySchedule', 'dailySchedule', 'monthlySchedule',
      'customSchedule', 'flexibleSchedule',
      'settings', 'isActive', 'isDefault', 'effectiveTo'
    ];

    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        (schedule as any)[field] = updateData[field];
      }
    });

    // If schedule type is being changed, validate the corresponding schedule data
    if (updateData.scheduleType && updateData.scheduleType !== schedule.scheduleType) {
      switch (updateData.scheduleType) {
        case 'weekly':
          if (!updateData.weeklySchedule) {
            createErrorResponse(res, 'weeklySchedule is required when changing to weekly schedule type', 400);
            return;
          }
          break;
        case 'daily':
          if (!updateData.dailySchedule) {
            createErrorResponse(res, 'dailySchedule is required when changing to daily schedule type', 400);
            return;
          }
          break;
        case 'monthly':
          if (!updateData.monthlySchedule) {
            createErrorResponse(res, 'monthlySchedule is required when changing to monthly schedule type', 400);
            return;
          }
          break;
        case 'custom':
          if (!updateData.customSchedule) {
            createErrorResponse(res, 'customSchedule is required when changing to custom schedule type', 400);
            return;
          }
          break;
        case 'flexible':
          if (!updateData.flexibleSchedule) {
            createErrorResponse(res, 'flexibleSchedule is required when changing to flexible schedule type', 400);
            return;
          }
          break;
      }
    }

    await schedule.save();

    // If setting as default, update calendar
    if (updateData.isDefault === true) {
      await Calendar.findByIdAndUpdate(schedule.calendarId, {
        hasSchedule: true,
        defaultScheduleId: schedule._id
      });
    }

    const updatedSchedule = await Schedule.findById(schedule._id)
      .populate('calendarId', 'name color')
      .populate('tutorId', 'firstname lastname email');

    res.json({
      success: true,
      message: 'Schedule updated successfully',
      data: updatedSchedule
    });

  } catch (error) {
    console.error('Error updating schedule:', error);
    createErrorResponse(res, 'Failed to update schedule', 500);
  }
};

/**
 * Delete schedule
 */
export const deleteSchedule = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can delete schedules', 403);
      return;
    }

    const { scheduleId } = req.params;

    if (!Types.ObjectId.isValid(scheduleId)) {
      createErrorResponse(res, 'Invalid schedule ID format', 400);
      return;
    }

    const schedule = await Schedule.findById(scheduleId);
    if (!schedule) {
      createErrorResponse(res, 'Schedule not found', 404);
      return;
    }

    // Verify ownership
    if (schedule.tutorId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'You do not own this schedule', 403);
      return;
    }

    // If this is the default schedule, update the calendar
    if (schedule.isDefault) {
      await Calendar.findByIdAndUpdate(schedule.calendarId, {
        hasSchedule: false,
        defaultScheduleId: null
      });
    }

    await schedule.deleteOne();

    res.json({
      success: true,
      message: 'Schedule deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting schedule:', error);
    createErrorResponse(res, 'Failed to delete schedule', 500);
  }
};

/**
 * Add or update schedule exception (holiday, vacation, etc.)
 */
export const addScheduleException = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can manage schedule exceptions', 403);
      return;
    }

    const { scheduleId } = req.params;
    const { date, type, title, description, isRecurring = false, replacementSchedule } = req.body;

    if (!Types.ObjectId.isValid(scheduleId)) {
      createErrorResponse(res, 'Invalid schedule ID format', 400);
      return;
    }

    if (!date || !type || !title) {
      createErrorResponse(res, 'date, type, and title are required', 400);
      return;
    }

    const schedule = await Schedule.findById(scheduleId);
    if (!schedule) {
      createErrorResponse(res, 'Schedule not found', 404);
      return;
    }

    // Verify ownership
    if (schedule.tutorId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'You do not own this schedule', 403);
      return;
    }

    const exception: IScheduleException = {
      date: new Date(date),
      type,
      title,
      description,
      isRecurring,
      replacementSchedule
    };

    // Remove existing exception for the same date if any
    schedule.exceptions = schedule.exceptions.filter((exc: IScheduleException) =>
      exc.date.toDateString() !== exception.date.toDateString()
    );

    schedule.exceptions.push(exception);
    await schedule.save();

    res.json({
      success: true,
      message: 'Schedule exception added successfully',
      data: { exception }
    });

  } catch (error) {
    console.error('Error adding schedule exception:', error);
    createErrorResponse(res, 'Failed to add schedule exception', 500);
  }
};

/**
 * Remove schedule exception
 */
/**
 * Get schedule booking analytics (which students are booking lessons)
 */
export const getScheduleBookingAnalytics = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can view schedule analytics', 403);
      return;
    }

    const { scheduleId } = req.params;
    const { startDate, endDate, limit = 50 } = req.query;

    if (!Types.ObjectId.isValid(scheduleId)) {
      createErrorResponse(res, 'Invalid schedule ID format', 400);
      return;
    }

    const schedule = await Schedule.findById(scheduleId);
    if (!schedule) {
      createErrorResponse(res, 'Schedule not found', 404);
      return;
    }

    // Verify ownership
    if (!req.user._id || schedule.tutorId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'You do not own this schedule', 403);
      return;
    }

    // Build date filter for lessons
    const dateFilter: any = {
      tutorId: req.user._id,
      status: { $in: ['scheduled', 'confirmed', 'completed', 'in_progress'] }
    };

    if (startDate || endDate) {
      dateFilter.startDateTime = {};
      if (startDate) dateFilter.startDateTime.$gte = new Date(startDate as string);
      if (endDate) dateFilter.startDateTime.$lte = new Date(endDate as string);
    }

    // Get lessons booked on this tutor's calendar
    const lessons = await Lesson.find(dateFilter)
      .populate('studentId', 'firstname lastname email avatar')
      .populate('subscriptionId', 'planType')
      .sort({ startDateTime: -1 })
      .limit(parseInt(limit as string));

    // Group lessons by student
    const studentBookings: { [key: string]: any } = {};
    const totalBookings = lessons.length;
    let totalRevenue = 0;

    lessons.forEach(lesson => {
      const studentId = (lesson.studentId as any)._id.toString();
      const studentInfo = lesson.studentId as any;

      if (!studentBookings[studentId]) {
        studentBookings[studentId] = {
          student: {
            id: studentId,
            name: `${studentInfo.firstname} ${studentInfo.lastname}`,
            email: studentInfo.email,
            avatar: studentInfo.avatar
          },
          totalLessons: 0,
          completedLessons: 0,
          upcomingLessons: 0,
          cancelledLessons: 0,
          totalRevenue: 0,
          averageRating: 0,
          lastBooking: null,
          subscriptionType: null,
          lessons: []
        };
      }

      const booking = studentBookings[studentId];
      booking.totalLessons++;
      booking.totalRevenue += lesson.price || 0;
      totalRevenue += lesson.price || 0;

      // Count lesson statuses
      if (lesson.status === 'completed') booking.completedLessons++;
      else if (lesson.status === 'cancelled') booking.cancelledLessons++;
      else if (['scheduled', 'confirmed', 'in_progress'].includes(lesson.status)) booking.upcomingLessons++;

      // Track subscription type
      if (lesson.subscriptionId) {
        booking.subscriptionType = (lesson.subscriptionId as any).planType;
      }

      // Update last booking date
      if (!booking.lastBooking || new Date(lesson.startDateTime) > new Date(booking.lastBooking)) {
        booking.lastBooking = lesson.startDateTime;
      }

      // Add lesson details
      booking.lessons.push({
        id: lesson._id,
        title: lesson.title,
        subject: lesson.subject,
        startDateTime: lesson.startDateTime,
        duration: lesson.duration,
        status: lesson.status,
        price: lesson.price,
        isTrialLesson: lesson.isTrialLesson
      });
    });

    // Convert to array and sort by total lessons
    const studentsArray = Object.values(studentBookings).sort((a: any, b: any) => b.totalLessons - a.totalLessons);

    // Calculate analytics
    const analytics = {
      totalStudents: studentsArray.length,
      totalBookings,
      totalRevenue,
      averageRevenuePerStudent: studentsArray.length > 0 ? Math.round(totalRevenue / studentsArray.length) : 0,
      averageLessonsPerStudent: studentsArray.length > 0 ? Math.round(totalBookings / studentsArray.length) : 0,
      topStudents: studentsArray.slice(0, 5),
      recentBookings: lessons.slice(0, 10).map(lesson => ({
        id: lesson._id,
        student: {
          id: (lesson.studentId as any)._id,
          name: `${(lesson.studentId as any).firstname} ${(lesson.studentId as any).lastname}`
        },
        title: lesson.title,
        startDateTime: lesson.startDateTime,
        status: lesson.status,
        price: lesson.price,
        bookedAt: lesson.createdAt
      }))
    };

    res.json({
      success: true,
      data: {
        schedule: {
          id: schedule._id,
          name: schedule.name,
          scheduleType: schedule.scheduleType
        },
        analytics,
        students: studentsArray
      }
    });

  } catch (error) {
    console.error('Error fetching schedule analytics:', error);
    createErrorResponse(res, 'Failed to fetch schedule analytics', 500);
  }
};

export const removeScheduleException = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can manage schedule exceptions', 403);
      return;
    }

    const { scheduleId } = req.params;
    const { date } = req.body;

    if (!Types.ObjectId.isValid(scheduleId)) {
      createErrorResponse(res, 'Invalid schedule ID format', 400);
      return;
    }

    if (!date) {
      createErrorResponse(res, 'date is required', 400);
      return;
    }

    const schedule = await Schedule.findById(scheduleId);
    if (!schedule) {
      createErrorResponse(res, 'Schedule not found', 404);
      return;
    }

    // Verify ownership
    if (schedule.tutorId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'You do not own this schedule', 403);
      return;
    }

    const exceptionDate = new Date(date);
    const initialLength = schedule.exceptions.length;

    schedule.exceptions = schedule.exceptions.filter((exc: IScheduleException) =>
      exc.date.toDateString() !== exceptionDate.toDateString()
    );

    if (schedule.exceptions.length === initialLength) {
      createErrorResponse(res, 'No exception found for the specified date', 404);
      return;
    }

    await schedule.save();

    res.json({
      success: true,
      message: 'Schedule exception removed successfully'
    });

  } catch (error) {
    console.error('Error removing schedule exception:', error);
    createErrorResponse(res, 'Failed to remove schedule exception', 500);
  }
};

/**
 * Get schedule availability for a date range
 */
export const getScheduleAvailability = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { scheduleId } = req.params;
    const { startDate, endDate, includeBooked = false } = req.query;

    if (!Types.ObjectId.isValid(scheduleId)) {
      createErrorResponse(res, 'Invalid schedule ID format', 400);
      return;
    }

    const schedule = await Schedule.findById(scheduleId)
      .populate('calendarId', 'name color');

    if (!schedule) {
      createErrorResponse(res, 'Schedule not found', 404);
      return;
    }

    // For non-tutors, only show public schedules
    if (!req.user || req.user.role !== 'tutor' ||
        (req.user.role === 'tutor' && schedule.tutorId.toString() !== (req.user._id as Types.ObjectId).toString())) {
      const calendar = await Calendar.findById(schedule.calendarId);
      if (!calendar || !calendar.isShared || !calendar.isActive) {
        createErrorResponse(res, 'Schedule not accessible', 403);
        return;
      }
    }

    const start = startDate ? new Date(startDate as string) : new Date();
    const end = endDate ? new Date(endDate as string) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

    const availability = [];
    const currentDate = new Date(start);

    while (currentDate <= end) {
      const daySchedule = schedule.getScheduleForDate(currentDate);

      if (daySchedule && daySchedule.isWorkingDay) {
        const availableSlots = schedule.getAvailableSlots(currentDate);

        // If requested, check for existing bookings (using Lesson model instead of Event)
        let slotsWithBookingInfo = availableSlots;
        if (includeBooked === 'true') {
          // Import Lesson model at the top if not already imported
          const { Lesson } = await import('../models/Lesson');

          const dayLessons = await Lesson.find({
            tutorCalendarId: schedule.calendarId,
            startDateTime: {
              $gte: new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate()),
              $lt: new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() + 1)
            },
            status: { $in: ['scheduled', 'confirmed', 'in_progress'] }
          });

          slotsWithBookingInfo = availableSlots.map((slot: ITimeSlot) => ({
            ...slot,
            isBooked: dayLessons.some((lesson: any) => {
              const lessonTime = lesson.startDateTime.toTimeString().substring(0, 5);
              return lessonTime >= slot.startTime && lessonTime < slot.endTime;
            })
          }));
        }

        availability.push({
          date: new Date(currentDate),
          dayOfWeek: currentDate.getDay(),
          isWorkingDay: true,
          slots: slotsWithBookingInfo,
          breakTimes: daySchedule.breakTimes || [],
          dayNotes: daySchedule.dayNotes
        });
      } else {
        availability.push({
          date: new Date(currentDate),
          dayOfWeek: currentDate.getDay(),
          isWorkingDay: false,
          slots: [],
          reason: daySchedule ? 'No working day' : 'Schedule exception'
        });
      }

      currentDate.setDate(currentDate.getDate() + 1);
    }

    res.json({
      success: true,
      data: {
        schedule: {
          _id: schedule._id,
          name: schedule.name,
          timezone: schedule.timezone,
          calendar: schedule.calendarId
        },
        dateRange: { startDate: start, endDate: end },
        availability
      }
    });

  } catch (error) {
    console.error('Error getting schedule availability:', error);
    createErrorResponse(res, 'Failed to get schedule availability', 500);
  }
};
