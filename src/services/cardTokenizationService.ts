import Stripe from 'stripe';
import crypto from 'crypto';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: '2025-05-28.basil',
});

export interface CardDetails {
  number: string;
  exp_month: number;
  exp_year: number;
  cvc: string;
  name?: string;
  address_line1?: string;
  address_line2?: string;
  address_city?: string;
  address_state?: string;
  address_zip?: string;
  address_country?: string;
}

export interface TokenizedCard {
  token: string;
  paymentMethodId?: string;
  last4: string;
  brand: string;
  exp_month: number;
  exp_year: number;
  fingerprint: string;
  created: Date;
  expiresAt: Date;
}

export interface TokenizationResult {
  success: boolean;
  tokenizedCard?: TokenizedCard;
  paymentMethodId?: string;
  error?: string;
  requiresAction?: boolean;
  clientSecret?: string;
}

/**
 * Card Tokenization Service
 * Handles secure tokenization of card details using Stripe
 */
export class CardTokenizationService {
  
  /**
   * Tokenize card details using Stripe
   */
  static async tokenizeCard(
    cardDetails: CardDetails,
    customerId?: string
  ): Promise<TokenizationResult> {
    try {
      // Validate card details first
      const validation = this.validateCardDetails(cardDetails);
      if (!validation.valid) {
        return {
          success: false,
          error: `Invalid card details: ${validation.errors.join(', ')}`
        };
      }

      // Create payment method with Stripe
      const paymentMethod = await stripe.paymentMethods.create({
        type: 'card',
        card: {
          number: cardDetails.number,
          exp_month: cardDetails.exp_month,
          exp_year: cardDetails.exp_year,
          cvc: cardDetails.cvc,
        },
        billing_details: {
          name: cardDetails.name,
          address: {
            line1: cardDetails.address_line1,
            line2: cardDetails.address_line2,
            city: cardDetails.address_city,
            state: cardDetails.address_state,
            postal_code: cardDetails.address_zip,
            country: cardDetails.address_country || 'US',
          },
        },
      });

      // Attach to customer if provided
      if (customerId) {
        await stripe.paymentMethods.attach(paymentMethod.id, {
          customer: customerId,
        });
      }

      // Generate secure token
      const token = this.generateSecureToken();
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 1); // Token expires in 1 hour

      const tokenizedCard: TokenizedCard = {
        token,
        paymentMethodId: paymentMethod.id,
        last4: paymentMethod.card?.last4 || '',
        brand: paymentMethod.card?.brand || '',
        exp_month: paymentMethod.card?.exp_month || 0,
        exp_year: paymentMethod.card?.exp_year || 0,
        fingerprint: paymentMethod.card?.fingerprint || '',
        created: new Date(),
        expiresAt,
      };

      // Store token mapping (in production, use Redis or secure database)
      await this.storeTokenMapping(token, tokenizedCard);

      return {
        success: true,
        tokenizedCard,
        paymentMethodId: paymentMethod.id,
      };

    } catch (error: any) {
      console.error('Card tokenization error:', error);
      
      if (error.type === 'StripeCardError') {
        return {
          success: false,
          error: error.message || 'Card was declined'
        };
      }

      return {
        success: false,
        error: 'Failed to tokenize card. Please try again.'
      };
    }
  }

  /**
   * Create payment method from existing token
   */
  static async createPaymentMethodFromToken(
    token: string,
    customerId?: string
  ): Promise<TokenizationResult> {
    try {
      const tokenData = await this.getTokenMapping(token);
      if (!tokenData) {
        return {
          success: false,
          error: 'Invalid or expired token'
        };
      }

      // Check if token is expired
      if (new Date() > tokenData.expiresAt) {
        await this.removeTokenMapping(token);
        return {
          success: false,
          error: 'Token has expired'
        };
      }

      // If payment method already exists, return it
      if (tokenData.paymentMethodId) {
        try {
          const paymentMethod = await stripe.paymentMethods.retrieve(tokenData.paymentMethodId);
          
          // Attach to customer if provided and not already attached
          if (customerId && !paymentMethod.customer) {
            await stripe.paymentMethods.attach(paymentMethod.id, {
              customer: customerId,
            });
          }

          return {
            success: true,
            tokenizedCard: tokenData,
            paymentMethodId: paymentMethod.id,
          };
        } catch (stripeError: any) {
          console.error('Error retrieving payment method:', stripeError);
          // Payment method might have been deleted, continue to create new one
        }
      }

      return {
        success: false,
        error: 'Payment method not found for token'
      };

    } catch (error: any) {
      console.error('Error creating payment method from token:', error);
      return {
        success: false,
        error: 'Failed to process token'
      };
    }
  }

  /**
   * Validate card details
   */
  private static validateCardDetails(cardDetails: CardDetails): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Validate card number
    if (!cardDetails.number || !this.isValidCardNumber(cardDetails.number)) {
      errors.push('Invalid card number');
    }
    
    // Validate expiration month
    if (!cardDetails.exp_month || cardDetails.exp_month < 1 || cardDetails.exp_month > 12) {
      errors.push('Invalid expiration month');
    }
    
    // Validate expiration year
    const currentYear = new Date().getFullYear();
    if (!cardDetails.exp_year || cardDetails.exp_year < currentYear) {
      errors.push('Card has expired');
    }
    
    // Validate CVC
    if (!cardDetails.cvc || cardDetails.cvc.length < 3 || cardDetails.cvc.length > 4) {
      errors.push('Invalid CVC');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate card number using Luhn algorithm
   */
  private static isValidCardNumber(cardNumber: string): boolean {
    const cleanNumber = cardNumber.replace(/\D/g, '');
    
    if (cleanNumber.length < 13 || cleanNumber.length > 19) {
      return false;
    }

    let sum = 0;
    let isEven = false;

    for (let i = cleanNumber.length - 1; i >= 0; i--) {
      let digit = parseInt(cleanNumber.charAt(i), 10);

      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      isEven = !isEven;
    }

    return sum % 10 === 0;
  }

  /**
   * Generate secure token
   */
  private static generateSecureToken(): string {
    return `ctok_${crypto.randomBytes(32).toString('hex')}`;
  }

  /**
   * Store token mapping (implement with Redis in production)
   */
  private static async storeTokenMapping(token: string, tokenData: TokenizedCard): Promise<void> {
    // In production, use Redis or secure database
    // For now, using in-memory storage (not suitable for production)
    if (!global.tokenStore) {
      global.tokenStore = new Map();
    }
    global.tokenStore.set(token, tokenData);
  }

  /**
   * Get token mapping
   */
  private static async getTokenMapping(token: string): Promise<TokenizedCard | null> {
    if (!global.tokenStore) {
      return null;
    }
    return global.tokenStore.get(token) || null;
  }

  /**
   * Remove token mapping
   */
  private static async removeTokenMapping(token: string): Promise<void> {
    if (global.tokenStore) {
      global.tokenStore.delete(token);
    }
  }

  /**
   * Clean expired tokens
   */
  static async cleanExpiredTokens(): Promise<void> {
    if (!global.tokenStore) {
      return;
    }

    const now = new Date();
    for (const [token, tokenData] of global.tokenStore.entries()) {
      if (now > tokenData.expiresAt) {
        global.tokenStore.delete(token);
      }
    }
  }
}

// Clean expired tokens every hour
setInterval(() => {
  CardTokenizationService.cleanExpiredTokens();
}, 60 * 60 * 1000);
