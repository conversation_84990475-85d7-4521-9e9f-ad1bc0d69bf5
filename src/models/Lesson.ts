import mongoose, { Schema, model, Document, Types } from 'mongoose';

export interface ILesson extends Document {
  studentId: Types.ObjectId;
  tutorId: Types.ObjectId;
  tutorCalendarId: Types.ObjectId;
  studentCalendarId?: Types.ObjectId;
  subscriptionId?: Types.ObjectId; // Reference to subscription if not trial
  
  // Lesson details
  title: string;
  description?: string;
  subject?: string;
  lessonType: 'regular' | 'trial' | 'makeup' | 'assessment';
  
  // Scheduling
  startDateTime: Date;
  endDateTime: Date;
  duration: number; // in minutes
  timezone: string;
  
  // Status
  status: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show';
  
  // Trial lesson specific
  isTrialLesson: boolean;
  trialUsed?: boolean; // Mark if this used the student's free trial
  
  // Lesson content and notes
  lessonNotes?: string;
  studentNotes?: string;
  tutorNotes?: string;
  homework?: string;
  
  // Attendance and completion
  studentAttended?: boolean;
  tutorAttended?: boolean;
  completedAt?: Date;
  
  // Feedback and rating
  studentFeedback?: {
    rating?: number; // 1-5
    comment?: string;
    wouldRecommend?: boolean;
  };
  tutorFeedback?: {
    studentPerformance?: number; // 1-5
    comment?: string;
    areasToImprove?: string[];
  };
  
  // Rescheduling
  originalDateTime?: Date;
  rescheduleReason?: string;
  rescheduleCount?: number;
  
  // Payment and billing
  price?: number;
  currency?: string;
  paymentStatus?: 'pending' | 'paid' | 'failed' | 'refunded' | 'free_trial';

  // Earnings tracking
  tutorEarningId?: Types.ObjectId; // Reference to tutor's earning record
  platformFee?: number; // Platform commission in cents
  tutorPayout?: number; // Amount tutor receives in cents
  paymentDeductedAt?: Date; // When payment was deducted from student
  earningsReleasedAt?: Date; // When earnings were released to tutor
  
  // Metadata
  metadata?: {
    createdBy: 'student' | 'tutor' | 'system';
    source?: string;
    remindersSent?: number;
    lastReminderAt?: Date;
    cancelledBy?: 'student' | 'tutor' | 'system';
    cancelledAt?: Date;
    cancellationReason?: string;
  };
  
  createdAt: Date;
  updatedAt: Date;
}

const lessonSchema = new Schema<ILesson>({
  studentId: { type: Schema.Types.ObjectId, ref: 'Student', required: true },
  tutorId: { type: Schema.Types.ObjectId, ref: 'Tutor', required: true },
  tutorCalendarId: { type: Schema.Types.ObjectId, ref: 'Calendar', required: true },
  studentCalendarId: { type: Schema.Types.ObjectId, ref: 'Calendar' },
  subscriptionId: { type: Schema.Types.ObjectId, ref: 'Subscription' },
  
  title: { type: String, required: true },
  description: String,
  subject: String,
  lessonType: { 
    type: String, 
    enum: ['regular', 'trial', 'makeup', 'assessment'], 
    default: 'regular' 
  },
  
  startDateTime: { type: Date, required: true },
  endDateTime: { type: Date, required: true },
  duration: { type: Number, required: true, min: 15 }, // minimum 15 minutes
  timezone: { type: String, default: 'UTC' },
  
  status: { 
    type: String, 
    enum: ['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'], 
    default: 'scheduled' 
  },
  
  isTrialLesson: { type: Boolean, default: false },
  trialUsed: { type: Boolean, default: false },
  
  lessonNotes: String,
  studentNotes: String,
  tutorNotes: String,
  homework: String,
  
  studentAttended: Boolean,
  tutorAttended: Boolean,
  completedAt: Date,
  
  studentFeedback: {
    rating: { type: Number, min: 1, max: 5 },
    comment: String,
    wouldRecommend: Boolean
  },
  tutorFeedback: {
    studentPerformance: { type: Number, min: 1, max: 5 },
    comment: String,
    areasToImprove: [String]
  },
  
  originalDateTime: Date,
  rescheduleReason: String,
  rescheduleCount: { type: Number, default: 0 },
  
  price: { type: Number, min: 0 },
  currency: { type: String, default: 'USD' },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'failed', 'refunded', 'free_trial'],
    default: 'pending'
  },

  tutorEarningId: { type: Schema.Types.ObjectId, ref: 'TutorEarning' },
  platformFee: { type: Number, min: 0 },
  tutorPayout: { type: Number, min: 0 },
  paymentDeductedAt: Date,
  earningsReleasedAt: Date,
  
  metadata: {
    createdBy: {
      type: String,
      enum: ['student', 'tutor', 'system'],
      required: true
    },
    source: String,
    remindersSent: { type: Number, default: 0 },
    lastReminderAt: Date,
    cancelledBy: {
      type: String,
      enum: ['student', 'tutor', 'system']
    },
    cancelledAt: Date,
    cancellationReason: String
  },
  
  createdAt: { type: Date, default: () => new Date() },
  updatedAt: { type: Date, default: () => new Date() }
});

// Indexes for better query performance
lessonSchema.index({ studentId: 1, startDateTime: -1 });
lessonSchema.index({ tutorId: 1, startDateTime: -1 });
lessonSchema.index({ tutorCalendarId: 1, startDateTime: 1 });
lessonSchema.index({ studentCalendarId: 1, startDateTime: 1 });
lessonSchema.index({ subscriptionId: 1 });
lessonSchema.index({ status: 1, startDateTime: 1 });
lessonSchema.index({ isTrialLesson: 1, studentId: 1 });
lessonSchema.index({ startDateTime: 1, endDateTime: 1 }); // For conflict checking
lessonSchema.index({ tutorId: 1, status: 1, startDateTime: 1 }); // For tutor dashboard
lessonSchema.index({ studentId: 1, status: 1, startDateTime: 1 }); // For student dashboard

// Pre-save middleware
lessonSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Auto-calculate duration if not set
  if (!this.duration && this.startDateTime && this.endDateTime) {
    this.duration = Math.floor((this.endDateTime.getTime() - this.startDateTime.getTime()) / (1000 * 60));
  }
  
  // Set trial lesson payment status
  if (this.isTrialLesson) {
    this.paymentStatus = 'free_trial';
    this.price = 0;
  }
  
  next();
});

// Post-save middleware to track student bookings
lessonSchema.post('save', async function(doc) {
  // Only track for new scheduled lessons
  if (doc.status === 'scheduled' && doc.isNew) {
    await doc.updateScheduleBookingTracker();
  }
});

// Instance methods
lessonSchema.methods.canBeRescheduled = function(): boolean {
  const now = new Date();
  const lessonStart = new Date(this.startDateTime);
  const hoursUntilLesson = (lessonStart.getTime() - now.getTime()) / (1000 * 60 * 60);
  
  return this.status === 'scheduled' && 
         hoursUntilLesson >= 24 && // At least 24 hours notice
         (this.rescheduleCount || 0) < 2; // Max 2 reschedules
};

lessonSchema.methods.canBeCancelled = function(): boolean {
  const now = new Date();
  const lessonStart = new Date(this.startDateTime);
  const hoursUntilLesson = (lessonStart.getTime() - now.getTime()) / (1000 * 60 * 60);
  
  return this.status === 'scheduled' && hoursUntilLesson >= 24;
};

lessonSchema.methods.markCompleted = function(attendanceData?: { studentAttended: boolean, tutorAttended: boolean }) {
  this.status = 'completed';
  this.completedAt = new Date();
  
  if (attendanceData) {
    this.studentAttended = attendanceData.studentAttended;
    this.tutorAttended = attendanceData.tutorAttended;
  }
  
  return this.save();
};

lessonSchema.methods.markNoShow = function(whoDidntShow: 'student' | 'tutor' | 'both') {
  this.status = 'no_show';
  
  if (whoDidntShow === 'student' || whoDidntShow === 'both') {
    this.studentAttended = false;
  }
  if (whoDidntShow === 'tutor' || whoDidntShow === 'both') {
    this.tutorAttended = false;
  }
  
  return this.save();
};

// Static methods
lessonSchema.statics.findUpcoming = function(userId: Types.ObjectId, userRole: 'student' | 'tutor', limit: number = 10) {
  const query: any = {
    startDateTime: { $gte: new Date() },
    status: { $in: ['scheduled', 'confirmed'] }
  };
  
  if (userRole === 'student') {
    query.studentId = userId;
  } else {
    query.tutorId = userId;
  }
  
  return this.find(query)
    .populate('studentId', 'firstname lastname email avatar')
    .populate('tutorId', 'firstname lastname email avatar')
    .populate('subscriptionId', 'planType remainingLessons')
    .sort({ startDateTime: 1 })
    .limit(limit);
};

lessonSchema.statics.checkTimeSlotConflict = async function(
  tutorId: Types.ObjectId,
  startDateTime: Date,
  endDateTime: Date,
  excludeLessonId?: Types.ObjectId
) {
  const query: any = {
    tutorId,
    status: { $in: ['scheduled', 'confirmed', 'in_progress'] },
    $or: [
      {
        startDateTime: { $lt: endDateTime },
        endDateTime: { $gt: startDateTime }
      }
    ]
  };

  if (excludeLessonId) {
    query._id = { $ne: excludeLessonId };
  }

  const conflictingLesson = await this.findOne(query);
  return !!conflictingLesson;
};

// Calendar integration methods
lessonSchema.methods.updateScheduleBookingTracker = async function() {
  try {
    const { Schedule } = await import('./Schedule');
    const calendar = await import('./calendar').then(m => m.Calendar.findById(this.tutorCalendarId));

    if (calendar && calendar.hasSchedule && calendar.defaultScheduleId) {
      const schedule = await Schedule.findById(calendar.defaultScheduleId);
      if (schedule) {
        const timeSlot = this.startDateTime.toTimeString().substring(0, 5); // HH:MM format
        schedule.trackStudentBooking(this.studentId, timeSlot);
        await schedule.save();
      }
    }
  } catch (error) {
    console.warn('Failed to update schedule booking tracker:', error);
  }
};

lessonSchema.statics.findByCalendar = function(
  calendarId: Types.ObjectId,
  startDate?: Date,
  endDate?: Date
) {
  const query: any = {
    $or: [
      { tutorCalendarId: calendarId },
      { studentCalendarId: calendarId }
    ]
  };

  if (startDate && endDate) {
    query.startDateTime = { $gte: startDate, $lte: endDate };
  }

  return this.find(query)
    .populate('studentId', 'firstname lastname email avatar')
    .populate('tutorId', 'firstname lastname email avatar')
    .sort({ startDateTime: 1 });
};

export const Lesson = (mongoose.models.Lesson as mongoose.Model<ILesson>) || model<ILesson>('Lesson', lessonSchema);
