import { Schema, model, Document, Types } from 'mongoose';

export interface ICalendar extends Document {
  ownerId: Types.ObjectId; // Can be student or tutor
  ownerType: 'student' | 'tutor';
  name: string;
  description?: string;
  color?: string;
  isShared?: boolean; // whether this calendar is visible to others
  timezone?: string; // Owner's timezone for proper scheduling
  isActive?: boolean; // Whether the calendar is currently active
  isDefault?: boolean; // Default calendar for the user

  // Schedule integration (primarily for tutors)
  hasSchedule?: boolean; // Whether this calendar uses schedule-based availability
  defaultScheduleId?: Types.ObjectId; // Reference to default schedule
  scheduleSettings?: {
    autoGenerateFromSchedule?: boolean; // Auto-generate events from schedule
    generateDaysAhead?: number; // How many days ahead to generate
    allowScheduleOverrides?: boolean; // Allow manual event overrides
  };

  // Student-specific preferences (when ownerType is 'student')
  studentPreferences?: {
    defaultView?: 'month' | 'week' | 'day' | 'agenda';
    showTutorCalendars?: boolean;
    reminderSettings?: {
      enabled?: boolean;
      minutesBefore?: number[];
    };
    availabilitySettings?: {
      preferredTimeSlots?: {
        dayOfWeek: number; // 0=Sunday, 1=Monday, etc.
        startTime: string; // "09:00"
        endTime: string; // "17:00"
      }[];
      blockedTimeSlots?: {
        dayOfWeek: number;
        startTime: string;
        endTime: string;
        reason?: string;
      }[];
    };
  };

  // Display settings
  displaySettings?: {
    showSchedulePattern?: boolean; // Show underlying schedule pattern in calendar view
    showAvailabilityOnly?: boolean; // Only show available slots to students
    groupSimilarSlots?: boolean; // Group similar time slots for cleaner display
  };

  // Access control
  accessSettings?: {
    allowPublicView?: boolean; // Allow public viewing of availability
    requireSubscription?: boolean; // Require active subscription to book
    allowTrialBookings?: boolean; // Allow trial lesson bookings
  };

  createdAt: Date;
  updatedAt: Date;
}

const calendarSchema = new Schema<ICalendar>({
  ownerId: { type: Schema.Types.ObjectId, required: true },
  ownerType: { type: String, enum: ['student', 'tutor'], required: true },
  name: { type: String, required: true },
  description: String,
  color: {
    type: String,
    default: function(this: ICalendar) {
      return this.ownerType === 'student' ? '#4F46E5' : '#3B82F6'; // Indigo for students, Blue for tutors
    }
  },
  isShared: { type: Boolean, default: true }, // Default to shared
  timezone: { type: String, default: 'UTC' },
  isActive: { type: Boolean, default: true },
  isDefault: { type: Boolean, default: false },

  // Schedule integration (primarily for tutors)
  hasSchedule: { type: Boolean, default: false },
  defaultScheduleId: { type: Schema.Types.ObjectId, ref: 'Schedule' },
  scheduleSettings: {
    autoGenerateFromSchedule: { type: Boolean, default: true },
    generateDaysAhead: { type: Number, default: 30, min: 1, max: 365 },
    allowScheduleOverrides: { type: Boolean, default: true }
  },

  // Student-specific preferences
  studentPreferences: {
    defaultView: { type: String, enum: ['month', 'week', 'day', 'agenda'], default: 'month' },
    showTutorCalendars: { type: Boolean, default: true },
    reminderSettings: {
      enabled: { type: Boolean, default: true },
      minutesBefore: { type: [Number], default: [15, 60] } // 15 minutes and 1 hour before
    },
    availabilitySettings: {
      preferredTimeSlots: [{
        dayOfWeek: { type: Number, min: 0, max: 6 },
        startTime: { type: String, match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/ },
        endTime: { type: String, match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/ }
      }],
      blockedTimeSlots: [{
        dayOfWeek: { type: Number, min: 0, max: 6 },
        startTime: { type: String, match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/ },
        endTime: { type: String, match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/ },
        reason: String
      }]
    }
  },

  // Display settings
  displaySettings: {
    showSchedulePattern: { type: Boolean, default: true },
    showAvailabilityOnly: { type: Boolean, default: false },
    groupSimilarSlots: { type: Boolean, default: true }
  },

  // Access control
  accessSettings: {
    allowPublicView: { type: Boolean, default: true },
    requireSubscription: { type: Boolean, default: false },
    allowTrialBookings: { type: Boolean, default: true }
  },

  createdAt: { type: Date, default: () => new Date() },
  updatedAt: { type: Date, default: () => new Date() }
});

// Add indexes for better query performance
calendarSchema.index({ ownerId: 1, ownerType: 1 });
calendarSchema.index({ ownerId: 1, ownerType: 1, isActive: 1 });
calendarSchema.index({ ownerId: 1, ownerType: 1, isDefault: 1 });
calendarSchema.index({ isShared: 1, isActive: 1 });
calendarSchema.index({ defaultScheduleId: 1 });
calendarSchema.index({ hasSchedule: 1, isActive: 1 });
calendarSchema.index({ ownerType: 1, isActive: 1 });
calendarSchema.index({ ownerType: 1, isDefault: 1, isActive: 1 });

calendarSchema.pre('save', function(next) {
  this.updatedAt = new Date();

  // Ensure only one default calendar per user
  if (this.isDefault && this.isModified('isDefault')) {
    Calendar.updateMany(
      {
        ownerId: this.ownerId,
        ownerType: this.ownerType,
        _id: { $ne: this._id },
        isDefault: true
      },
      { isDefault: false }
    ).exec();
  }

  // Validate student preferences only exist for students
  if (this.ownerType === 'tutor' && this.studentPreferences) {
    this.studentPreferences = undefined;
  }

  // Validate schedule settings only exist for tutors
  if (this.ownerType === 'student' && (this.hasSchedule || this.defaultScheduleId)) {
    this.hasSchedule = false;
    this.defaultScheduleId = undefined;
    this.scheduleSettings = undefined;
  }

  next();
});

// Instance methods

// Schedule-related methods
calendarSchema.methods.hasActiveSchedule = function(): boolean {
  return this.ownerType === 'tutor' && this.hasSchedule && this.isActive && !!this.defaultScheduleId;
};





calendarSchema.methods.allowsScheduleOverrides = function(): boolean {
  return this.scheduleSettings?.allowScheduleOverrides !== false;
};

calendarSchema.methods.getDisplayMode = function(): 'schedule' | 'hybrid' {
  if (!this.hasSchedule) return 'schedule';
  if (this.displaySettings?.showSchedulePattern) return 'hybrid';
  return 'schedule';
};

// User-specific methods
calendarSchema.methods.isStudentCalendar = function(): boolean {
  return this.ownerType === 'student';
};

calendarSchema.methods.isTutorCalendar = function(): boolean {
  return this.ownerType === 'tutor';
};

calendarSchema.methods.canBeBookedBy = function(studentId: Types.ObjectId): boolean {
  if (!this.isActive || !this.isShared) return false;
  if (this.ownerType === 'student') return false;

  // Additional checks can be added here based on subscription status, etc.
  return true;
};

calendarSchema.methods.getPreferences = function() {
  if (this.ownerType === 'student') {
    return this.studentPreferences || {};
  }
  return this.displaySettings || {};
};

// Schedule integration methods
calendarSchema.methods.setDefaultSchedule = async function(scheduleId: Types.ObjectId) {
  this.hasSchedule = true;
  this.defaultScheduleId = scheduleId;

  // Ensure schedule settings are initialized
  if (!this.scheduleSettings) {
    this.scheduleSettings = {
      autoGenerateFromSchedule: true,
      generateDaysAhead: 30,
      allowScheduleOverrides: true
    };
  }

  return this.save();
};

calendarSchema.methods.removeSchedule = async function() {
  this.hasSchedule = false;
  this.defaultScheduleId = undefined;
  this.scheduleSettings = undefined;

  return this.save();
};

calendarSchema.methods.canAcceptBookings = function(): boolean {
  if (!this.isActive || this.ownerType !== 'tutor') return false;
  if (!this.isShared) return false;

  // Check access settings
  if (this.accessSettings?.allowPublicView === false) return false;

  return true;
};

calendarSchema.methods.requiresSubscription = function(): boolean {
  return this.accessSettings?.requireSubscription === true;
};

calendarSchema.methods.allowsTrialBookings = function(): boolean {
  return this.accessSettings?.allowTrialBookings !== false;
};

// Static methods for finding calendars
calendarSchema.statics.findDefaultForUser = function(userId: Types.ObjectId, userType: 'student' | 'tutor') {
  return this.findOne({
    ownerId: userId,
    ownerType: userType,
    isDefault: true,
    isActive: true
  });
};

calendarSchema.statics.findActiveForUser = function(userId: Types.ObjectId, userType: 'student' | 'tutor') {
  return this.find({
    ownerId: userId,
    ownerType: userType,
    isActive: true
  }).sort({ isDefault: -1, createdAt: -1 });
};

export const Calendar = model<ICalendar>('Calendar', calendarSchema);
