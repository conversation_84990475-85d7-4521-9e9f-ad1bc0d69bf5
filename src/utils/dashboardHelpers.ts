import { Types } from 'mongoose';
import { Lesson } from '../models/Lesson';
import { Calendar } from '../models/calendar';
import { Schedule } from '../models/Schedule';
import Subscription from '../models/subscription.model';

export interface DashboardMetrics {
  totalLessons: number;
  completedLessons: number;
  upcomingLessons: number;
  cancelledLessons: number;
  totalHours: number;
  averageDuration: number;
}

export interface FormattedLesson {
  id: string;
  title: string;
  subject?: string;
  description?: string;
  startDateTime: Date;
  endDateTime: Date;
  duration: number;
  timezone: string;
  status: string;
  lessonType: string;
  isTrialLesson: boolean;
  student?: {
    id: string;
    name: string;
    email?: string;
    profilePicture?: string;
  };
  tutor?: {
    id: string;
    name: string;
    email?: string;
    profilePicture?: string;
    subjects?: string[];
  };
  subscription?: {
    planType: string;
    lessonsPerWeek: number;
    monthlyPrice?: number;
  };
}

export interface CalendarInfo {
  id: string;
  name: string;
  color: string;
  hasSchedule: boolean;
  defaultScheduleId?: string;
  isActive: boolean;
}

export interface ScheduleInfo {
  id: string;
  name: string;
  description?: string;
  timezone: string;
  isDefault: boolean;
  weeklySchedule: any;
  calendar?: {
    id: string;
    name: string;
    color: string;
  };
}

/**
 * Calculate comprehensive lesson metrics for a user
 */
export const calculateLessonMetrics = async (
  userId: Types.ObjectId,
  userType: 'student' | 'tutor'
): Promise<DashboardMetrics> => {
  const userField = userType === 'student' ? 'studentId' : 'tutorId';
  
  const [totalLessons, completedLessons, upcomingLessons, cancelledLessons] = await Promise.all([
    Lesson.countDocuments({ [userField]: userId }),
    Lesson.countDocuments({ [userField]: userId, status: 'completed' }),
    Lesson.countDocuments({ 
      [userField]: userId, 
      status: { $in: ['scheduled', 'confirmed'] },
      startDateTime: { $gte: new Date() }
    }),
    Lesson.countDocuments({ [userField]: userId, status: 'cancelled' })
  ]);

  // Calculate total hours
  const totalHoursResult = await Lesson.aggregate([
    {
      $match: {
        [userField]: userId,
        status: 'completed'
      }
    },
    {
      $group: {
        _id: null,
        totalMinutes: { $sum: '$duration' }
      }
    }
  ]);

  const totalMinutes = totalHoursResult[0]?.totalMinutes || 0;
  const totalHours = Math.round((totalMinutes / 60) * 100) / 100;
  const averageDuration = completedLessons > 0 ? Math.round(totalMinutes / completedLessons) : 0;

  return {
    totalLessons,
    completedLessons,
    upcomingLessons,
    cancelledLessons,
    totalHours,
    averageDuration
  };
};

/**
 * Format lesson data for dashboard display
 */
export const formatLessonsForDashboard = (lessons: any[], userType: 'student' | 'tutor'): FormattedLesson[] => {
  return lessons.map(lesson => {
    const formatted: FormattedLesson = {
      id: lesson._id.toString(),
      title: lesson.title,
      subject: lesson.subject,
      description: lesson.description,
      startDateTime: lesson.startDateTime,
      endDateTime: lesson.endDateTime,
      duration: lesson.duration,
      timezone: lesson.timezone,
      status: lesson.status,
      lessonType: lesson.lessonType,
      isTrialLesson: lesson.isTrialLesson
    };

    // Add student info for tutor dashboard
    if (userType === 'tutor' && lesson.studentId) {
      formatted.student = {
        id: lesson.studentId._id.toString(),
        name: `${lesson.studentId.firstname} ${lesson.studentId.lastname}`,
        email: lesson.studentId.email,
        profilePicture: lesson.studentId.profilePicture
      };
    }

    // Add tutor info for student dashboard
    if (userType === 'student' && lesson.tutorId) {
      formatted.tutor = {
        id: lesson.tutorId._id.toString(),
        name: `${lesson.tutorId.firstname} ${lesson.tutorId.lastname}`,
        email: lesson.tutorId.email,
        profilePicture: lesson.tutorId.profilePicture,
        subjects: lesson.tutorId.teachingSubjects
      };
    }

    // Add subscription info if available
    if (lesson.subscriptionId) {
      formatted.subscription = {
        planType: lesson.subscriptionId.planType,
        lessonsPerWeek: lesson.subscriptionId.lessonsPerWeek,
        monthlyPrice: lesson.subscriptionId.monthlyPrice
      };
    }

    return formatted;
  });
};

/**
 * Get calendar information for a user
 */
export const getUserCalendars = async (
  userId: Types.ObjectId,
  userType: 'student' | 'tutor'
): Promise<CalendarInfo[]> => {
  const calendars = await Calendar.find({
    ownerId: userId,
    ownerType: userType,
    isActive: true
  }).select('name color hasSchedule defaultScheduleId isActive');

  return calendars.map(cal => ({
    id: (cal._id as any).toString(),
    name: cal.name,
    color: cal.color || '#3B82F6',
    hasSchedule: cal.hasSchedule || false,
    defaultScheduleId: cal.defaultScheduleId?.toString(),
    isActive: cal.isActive || false
  }));
};

/**
 * Get schedule information for a tutor
 */
export const getTutorSchedules = async (tutorId: Types.ObjectId): Promise<ScheduleInfo[]> => {
  const schedules = await Schedule.find({
    tutorId: tutorId,
    isActive: true
  })
  .populate('calendarId', 'name color')
  .select('name description timezone weeklySchedule isDefault calendarId');

  return schedules.map(schedule => ({
    id: (schedule._id as any).toString(),
    name: schedule.name,
    description: schedule.description,
    timezone: schedule.timezone,
    isDefault: schedule.isDefault,
    weeklySchedule: schedule.weeklySchedule,
    calendar: schedule.calendarId ? {
      id: (schedule.calendarId as any)._id.toString(),
      name: (schedule.calendarId as any).name,
      color: (schedule.calendarId as any).color
    } : undefined
  }));
};

/**
 * Group lessons by date for calendar display
 */
export const groupLessonsByDate = (lessons: FormattedLesson[]): Record<string, FormattedLesson[]> => {
  return lessons.reduce((acc, lesson) => {
    const dateKey = lesson.startDateTime.toISOString().split('T')[0];
    if (!acc[dateKey]) {
      acc[dateKey] = [];
    }
    acc[dateKey].push(lesson);
    return acc;
  }, {} as Record<string, FormattedLesson[]>);
};

/**
 * Calculate subscription metrics for a student
 */
export const calculateSubscriptionMetrics = async (studentId: Types.ObjectId) => {
  const [activeSubscriptions, totalSubscriptions] = await Promise.all([
    Subscription.find({
      studentId: studentId,
      status: 'active'
    }).populate('tutorId', 'firstname lastname email teachingSubjects rating profilePicture'),
    Subscription.countDocuments({ studentId: studentId })
  ]);

  const totalRemainingLessons = activeSubscriptions.reduce((total, sub) => total + sub.remainingLessons, 0);

  return {
    active: activeSubscriptions.length,
    total: totalSubscriptions,
    totalRemainingLessons,
    activeSubscriptions: activeSubscriptions.map(sub => ({
      id: sub._id.toString(),
      tutorName: `${(sub.tutorId as any).firstname} ${(sub.tutorId as any).lastname}`,
      tutorEmail: (sub.tutorId as any).email,
      tutorSubjects: (sub.tutorId as any).teachingSubjects,
      tutorRating: (sub.tutorId as any).rating,
      tutorProfilePicture: (sub.tutorId as any).profilePicture,
      planType: sub.planType,
      lessonsPerWeek: sub.lessonsPerWeek,
      remainingLessons: sub.remainingLessons,
      currentPeriodEnd: sub.currentPeriodEnd,
      nextBillingDate: sub.nextBillingDate,
      monthlyPrice: sub.monthlyPrice,
      status: sub.status
    }))
  };
};

/**
 * Get date range for dashboard queries
 */
export const getDateRange = (days: number = 7) => {
  const startDate = new Date();
  const endDate = new Date();
  endDate.setDate(startDate.getDate() + days);
  
  return { startDate, endDate };
};

/**
 * Get today's date range (start and end of day)
 */
export const getTodayRange = () => {
  const today = new Date();
  const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
  
  return { startOfDay, endOfDay };
};
