import { Types } from 'mongoose';
import { Calendar } from '../models/calendar';
import { Schedule } from '../models/Schedule';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Validate calendar and schedule relationship
 */
export async function validateCalendarScheduleRelationship(
  calendarId: Types.ObjectId,
  scheduleId?: Types.ObjectId
): Promise<ValidationResult> {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  };

  try {
    // Get calendar
    const calendar = await Calendar.findById(calendarId);
    if (!calendar) {
      result.errors.push('Calendar not found');
      result.isValid = false;
      return result;
    }

    // Validate calendar is for tutor if schedule is provided
    if (scheduleId && calendar.ownerType !== 'tutor') {
      result.errors.push('Only tutor calendars can have schedules');
      result.isValid = false;
    }

    // Get schedule if provided
    if (scheduleId) {
      const schedule = await Schedule.findById(scheduleId);
      if (!schedule) {
        result.errors.push('Schedule not found');
        result.isValid = false;
        return result;
      }

      // Validate schedule belongs to the same calendar
      if (schedule.calendarId.toString() !== calendarId.toString()) {
        result.errors.push('Schedule does not belong to the specified calendar');
        result.isValid = false;
      }

      // Validate schedule belongs to the same tutor
      if (schedule.tutorId.toString() !== calendar.ownerId.toString()) {
        result.errors.push('Schedule tutor does not match calendar owner');
        result.isValid = false;
      }

      // Validate timezone consistency
      if (schedule.timezone !== calendar.timezone) {
        result.warnings.push('Schedule and calendar have different timezones');
      }

      // Validate schedule is active if calendar expects it
      if (calendar.hasSchedule && !schedule.isActive) {
        result.warnings.push('Calendar expects active schedule but schedule is inactive');
      }
    }

    // Validate calendar settings consistency
    if (calendar.hasSchedule && !calendar.defaultScheduleId) {
      result.errors.push('Calendar marked as having schedule but no default schedule ID set');
      result.isValid = false;
    }

    if (!calendar.hasSchedule && calendar.defaultScheduleId) {
      result.warnings.push('Calendar has default schedule ID but is not marked as having schedule');
    }

  } catch (error) {
    result.errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    result.isValid = false;
  }

  return result;
}

/**
 * Validate schedule data consistency
 */
export function validateScheduleData(scheduleData: any): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  };

  // Validate schedule type has corresponding data
  switch (scheduleData.scheduleType) {
    case 'weekly':
      if (!scheduleData.weeklySchedule || scheduleData.weeklySchedule.length === 0) {
        result.errors.push('Weekly schedule type requires weeklySchedule data');
        result.isValid = false;
      }
      break;
    
    case 'daily':
      if (!scheduleData.dailySchedule) {
        result.errors.push('Daily schedule type requires dailySchedule data');
        result.isValid = false;
      }
      break;
    
    case 'monthly':
      if (!scheduleData.monthlySchedule || scheduleData.monthlySchedule.length === 0) {
        result.errors.push('Monthly schedule type requires monthlySchedule data');
        result.isValid = false;
      }
      break;
    
    case 'custom':
      if (!scheduleData.customSchedule || !scheduleData.customSchedule.specificDates || 
          scheduleData.customSchedule.specificDates.length === 0) {
        result.errors.push('Custom schedule type requires customSchedule with specificDates');
        result.isValid = false;
      }
      break;
    
    case 'flexible':
      if (!scheduleData.flexibleSchedule) {
        result.errors.push('Flexible schedule type requires flexibleSchedule data');
        result.isValid = false;
      }
      break;
  }

  // Validate time slots don't overlap
  if (scheduleData.weeklySchedule) {
    for (const daySchedule of scheduleData.weeklySchedule) {
      const overlaps = validateTimeSlotOverlaps(daySchedule.timeSlots);
      if (overlaps.length > 0) {
        result.errors.push(`Overlapping time slots on ${getDayName(daySchedule.dayOfWeek)}: ${overlaps.join(', ')}`);
        result.isValid = false;
      }
    }
  }

  // Validate settings consistency
  if (scheduleData.settings) {
    if (scheduleData.settings.generateDaysAhead > 365) {
      result.warnings.push('Generate days ahead is very high (>365 days)');
    }
    
    if (scheduleData.settings.defaultSessionDuration < 15) {
      result.warnings.push('Default session duration is very short (<15 minutes)');
    }
    
    if (scheduleData.settings.maxDailyBookings && scheduleData.settings.maxDailyBookings > 20) {
      result.warnings.push('Maximum daily bookings is very high (>20)');
    }
  }

  return result;
}

/**
 * Validate time slots for overlaps
 */
function validateTimeSlotOverlaps(timeSlots: any[]): string[] {
  const overlaps: string[] = [];
  
  if (!timeSlots || timeSlots.length < 2) return overlaps;
  
  const sortedSlots = timeSlots
    .filter(slot => slot.sessionType !== 'break')
    .sort((a, b) => a.startTime.localeCompare(b.startTime));
  
  for (let i = 0; i < sortedSlots.length - 1; i++) {
    const current = sortedSlots[i];
    const next = sortedSlots[i + 1];
    
    if (current.endTime > next.startTime) {
      overlaps.push(`${current.startTime}-${current.endTime} overlaps with ${next.startTime}-${next.endTime}`);
    }
  }
  
  return overlaps;
}

/**
 * Validate lesson booking against calendar and schedule
 */
export async function validateLessonBooking(
  studentId: Types.ObjectId,
  tutorCalendarId: Types.ObjectId,
  startDateTime: Date,
  duration: number
): Promise<ValidationResult> {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  };

  try {
    // Get calendar
    const calendar = await Calendar.findById(tutorCalendarId);
    if (!calendar) {
      result.errors.push('Tutor calendar not found');
      result.isValid = false;
      return result;
    }

    // Validate calendar can accept bookings
    if (!(calendar as any).canAcceptBookings()) {
      result.errors.push('Calendar does not accept bookings');
      result.isValid = false;
    }

    // Check if calendar has schedule
    if (calendar.hasSchedule && calendar.defaultScheduleId) {
      const schedule = await Schedule.findById(calendar.defaultScheduleId);
      if (schedule) {
        const timeString = startDateTime.toTimeString().substring(0, 5); // HH:MM format
        
        if (!schedule.isAvailableAt(startDateTime, timeString)) {
          result.errors.push('Requested time slot is not available in tutor schedule');
          result.isValid = false;
        }
      }
    }

    // Validate duration
    if (duration < 15) {
      result.errors.push('Lesson duration must be at least 15 minutes');
      result.isValid = false;
    }

    if (duration > 480) { // 8 hours
      result.warnings.push('Lesson duration is very long (>8 hours)');
    }

  } catch (error) {
    result.errors.push(`Booking validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    result.isValid = false;
  }

  return result;
}

/**
 * Helper function to get day name
 */
function getDayName(dayOfWeek: number): string {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  return days[dayOfWeek] || 'Unknown';
}

/**
 * Validate calendar creation data
 */
export function validateCalendarData(calendarData: any): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  };

  // Required fields
  if (!calendarData.ownerId) {
    result.errors.push('Owner ID is required');
    result.isValid = false;
  }

  if (!calendarData.ownerType || !['student', 'tutor'].includes(calendarData.ownerType)) {
    result.errors.push('Owner type must be either "student" or "tutor"');
    result.isValid = false;
  }

  if (!calendarData.name || calendarData.name.trim().length === 0) {
    result.errors.push('Calendar name is required');
    result.isValid = false;
  }

  // Validate student preferences only for students
  if (calendarData.ownerType === 'tutor' && calendarData.studentPreferences) {
    result.warnings.push('Student preferences will be ignored for tutor calendars');
  }

  // Validate schedule settings only for tutors
  if (calendarData.ownerType === 'student' && (calendarData.hasSchedule || calendarData.scheduleSettings)) {
    result.warnings.push('Schedule settings will be ignored for student calendars');
  }

  return result;
}
