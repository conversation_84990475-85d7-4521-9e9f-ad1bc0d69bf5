import { Router } from 'express';
import {
  scheduleLesson,
  getMyLessons,
  getTutorSchedule,
  cancelLesson,
  completeLesson,
  getStudentBookingHistory
} from '../controllers/lessonController';
import { isAuthenticated } from '../middlewares/auth';
import { validateSubscription } from '../middlewares/subscriptionValidation';
import { ensureBothUsersHaveCalendars } from '../middlewares/ensureCalendar';

const router = Router();

// Student routes - schedule lessons with tutors
router.post('/schedule', ...isAuthenticated(), ensureBothUsersHaveCalendars, validateSubscription, scheduleLesson);

// Get user's lessons (student or tutor)
router.get('/my-lessons', ...isAuthenticated(), getMyLessons);

// Get tutor's schedule (for students to view available times)
router.get('/tutor/:tutorId/schedule', ...isAuthenticated(), getTutorSchedule);

// Cancel a lesson (student or tutor)
router.patch('/:lessonId/cancel', ...isAuthenticated(), cancelLesson);

// Complete a lesson (tutor only)
router.patch('/:lessonId/complete', ...isAuthenticated(), completeLesson);

// Get student booking history (tutor only)
router.get('/student-bookings', ...isAuthenticated(), getStudentBookingHistory);

export default router;
