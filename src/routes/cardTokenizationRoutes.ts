import { Router } from 'express';
import {
  tokenizeCard,
  validateCard,
  getTokenizedCard,
  getPaymentMethods,
  deletePaymentMethod
} from '../controllers/cardTokenizationController';
import { isAuthenticated } from '../middlewares/auth';
import { withRequestBody } from '../middlewares/misc';

const router = Router();

/**
 * POST /api/cards/tokenize
 * Tokenize card details for secure storage
 * Body: { number, exp_month, exp_year, cvc, name?, address_line1?, address_city?, address_state?, address_zip?, address_country? }
 */
router.post('/tokenize',
  withRequestBody(),
  isAuthenticated(),
  tokenizeCard
);

/**
 * POST /api/cards/validate
 * Validate card details without storing
 * Body: { number, exp_month, exp_year, cvc }
 */
router.post('/validate',
  withRequestBody(),
  isAuthenticated(),
  validateCard
);

/**
 * GET /api/cards/token/:token
 * Get tokenized card information by token
 */
router.get('/token/:token',
  isAuthenticated(),
  getTokenizedCard
);

/**
 * GET /api/cards/payment-methods
 * Get user's saved payment methods
 */
router.get('/payment-methods',
  isAuthenticated(),
  getPaymentMethods
);

/**
 * DELETE /api/cards/payment-methods/:paymentMethodId
 * Delete a saved payment method
 */
router.delete('/payment-methods/:paymentMethodId',
  isAuthenticated(),
  deletePaymentMethod
);

export default router;
