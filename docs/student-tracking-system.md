# Student ID Collection and Tracking System

This document describes how the system collects and tracks student IDs when learners schedule lessons on tutor calendars.

## Overview

The system automatically captures and tracks student information whenever a learner schedules a lesson with a tutor. This provides comprehensive analytics for tutors to understand their student base and booking patterns.

## Student ID Collection Points

### 1. Lesson Scheduling
When students schedule lessons, the system automatically captures:

```javascript
// Automatic collection during lesson scheduling
{
  studentId: req.user._id,           // Student's unique ID
  tutorId: tutorId,                  // <PERSON><PERSON>'s ID
  tutorCalendarId: tutorCalendarId,  // Which calendar was used
  studentCalendarId: studentCalendar._id, // Student's calendar
  subscriptionId: subscription?._id,  // Associated subscription
  lessonType: 'regular' | 'trial',   // Type of lesson
  startDateTime: Date,               // When lesson is scheduled
  duration: number,                  // Lesson duration in minutes
  subject: string,                   // Subject being taught
  price: number,                     // Lesson price
  bookedAt: Date,                    // When booking was made
  source: 'web_app'                  // Booking source
}
```

### 2. Enhanced Logging
The system logs detailed booking activities:

```javascript
console.log(`📅 Student Booking Activity:`, {
  studentId: req.user._id,
  studentName: "John Doe",
  tutorId: tutorId,
  tutorCalendarId: tutorCalendarId,
  lessonId: lesson._id,
  lessonType: "regular",
  startDateTime: "2024-01-15T10:00:00Z",
  duration: 60,
  subject: "Mathematics",
  price: 5000, // $50.00 in cents
  isTrialLesson: false,
  subscriptionId: "subscription_id",
  bookedAt: "2024-01-10T14:30:00Z",
  source: "web_app"
});
```

## API Endpoints for Student Tracking

### 1. Schedule Analytics (Tutor Only)
```bash
GET /api/schedules/:scheduleId/analytics?startDate=2024-01-01&endDate=2024-12-31&limit=50
```

**Response:**
```json
{
  "success": true,
  "data": {
    "schedule": {
      "id": "schedule_id",
      "name": "My Weekly Schedule",
      "scheduleType": "weekly"
    },
    "analytics": {
      "totalStudents": 15,
      "totalBookings": 45,
      "totalRevenue": 225000,
      "averageRevenuePerStudent": 15000,
      "averageLessonsPerStudent": 3,
      "topStudents": [...],
      "recentBookings": [...]
    },
    "students": [
      {
        "student": {
          "id": "student_id",
          "name": "John Doe",
          "email": "<EMAIL>",
          "avatar": "avatar_url"
        },
        "totalLessons": 8,
        "completedLessons": 6,
        "upcomingLessons": 2,
        "cancelledLessons": 0,
        "totalRevenue": 40000,
        "lastBooking": "2024-01-15T10:00:00Z",
        "subscriptionType": "premium",
        "lessons": [...]
      }
    ]
  }
}
```

### 2. Student Booking History (Tutor Only)
```bash
GET /api/lessons/student-bookings?startDate=2024-01-01&endDate=2024-12-31&limit=50&studentId=student_id
```

**Response:**
```json
{
  "success": true,
  "data": {
    "overallStats": {
      "totalStudents": 12,
      "totalBookings": 38,
      "totalRevenue": 190000,
      "totalHours": 63.5,
      "averageRevenuePerStudent": 15833,
      "averageBookingsPerStudent": 3,
      "completionRate": 89,
      "trialConversionRate": 75
    },
    "students": [
      {
        "student": {
          "id": "student_id",
          "name": "Jane Smith",
          "email": "<EMAIL>",
          "avatar": "avatar_url"
        },
        "totalBookings": 5,
        "completedLessons": 4,
        "upcomingLessons": 1,
        "cancelledLessons": 0,
        "trialLessons": 1,
        "totalRevenue": 20000,
        "totalHours": 5,
        "firstBooking": "2024-01-01T09:00:00Z",
        "lastBooking": "2024-01-15T10:00:00Z",
        "subscriptionType": "standard",
        "recentLessons": [...]
      }
    ]
  }
}
```

## Data Collected for Each Student

### Basic Information
- **Student ID**: Unique identifier
- **Name**: First and last name
- **Email**: Contact email
- **Avatar**: Profile picture URL

### Booking Patterns
- **Total Bookings**: Number of lessons scheduled
- **Lesson Status Breakdown**:
  - Completed lessons
  - Upcoming lessons
  - Cancelled lessons
  - Trial lessons

### Financial Data
- **Total Revenue**: Amount paid by student
- **Average Lesson Price**: Revenue per lesson
- **Subscription Type**: Current subscription plan

### Temporal Data
- **First Booking**: When student first scheduled
- **Last Booking**: Most recent booking
- **Booking Frequency**: How often student books

### Lesson Details
- **Subjects**: What subjects student studies
- **Duration Preferences**: Typical lesson length
- **Time Preferences**: Preferred booking times
- **Cancellation Rate**: How often student cancels

## Use Cases for Tutors

### 1. Student Relationship Management
- Track which students book most frequently
- Identify loyal students vs. one-time bookings
- Monitor student engagement over time

### 2. Revenue Analytics
- See which students generate most revenue
- Track average revenue per student
- Identify high-value students

### 3. Schedule Optimization
- Understand peak booking times
- See which schedule types attract most students
- Optimize availability based on demand

### 4. Student Retention
- Monitor trial-to-paid conversion rates
- Track student booking patterns
- Identify students at risk of churning

## Privacy and Security

### Data Protection
- Student IDs are only visible to the tutor they book with
- Personal information is limited to name, email, and avatar
- No sensitive financial data is exposed

### Access Control
- Only tutors can view their own student analytics
- Students cannot see other students' data
- Admin oversight for data protection compliance

### Data Retention
- Booking history is maintained for analytics
- Personal data follows platform privacy policy
- Students can request data deletion

## Example Workflow

1. **Student Books Lesson**:
   ```
   Student "John Doe" books 60-min Math lesson with Tutor "Jane Smith"
   → System captures: studentId, tutorId, lesson details, booking time
   → Logs booking activity with full details
   ```

2. **Tutor Views Analytics**:
   ```
   Tutor accesses /api/schedules/schedule_id/analytics
   → Sees John Doe has booked 3 lessons, spent $150, 100% completion rate
   → Can track John's booking pattern and preferences
   ```

3. **Revenue Tracking**:
   ```
   System automatically tracks:
   → John's total revenue: $150
   → Average per lesson: $50
   → Booking frequency: Weekly
   → Subscription: Premium plan
   ```

## Benefits

### For Tutors
- **Better Student Understanding**: Know your students better
- **Revenue Optimization**: Focus on high-value students
- **Schedule Planning**: Optimize based on demand patterns
- **Retention Strategies**: Identify and retain loyal students

### For Platform
- **Data Insights**: Understand booking patterns platform-wide
- **Quality Metrics**: Track tutor-student relationships
- **Business Intelligence**: Revenue and engagement analytics

This comprehensive tracking system ensures tutors have full visibility into their student base while maintaining privacy and security standards.
