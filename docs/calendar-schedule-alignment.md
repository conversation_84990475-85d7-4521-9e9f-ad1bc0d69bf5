# Calendar and Schedule Model Alignment

## Overview
This document outlines the improvements made to align the calendar and schedule models for better integration and consistency.

## Changes Made

### 1. Consolidated Calendar Models
- **Removed**: `src/models/StudentCalendar.ts` (duplicate functionality)
- **Enhanced**: `src/models/calendar.ts` to handle both student and tutor calendars
- **Added**: Student-specific preferences within the main Calendar model
- **Added**: Better validation to ensure student/tutor-specific fields are properly handled

### 2. Enhanced Calendar Model Features

#### New Interface Properties:
- `isDefault`: Mark default calendar for users
- `studentPreferences`: Student-specific settings (only for student calendars)
- `accessSettings`: Control booking permissions and trial lesson settings

#### New Methods:
- `hasActiveSchedule()`: Check if calendar has active schedule (tutor-only)
- `isStudentCalendar()` / `isTutorCalendar()`: Type checking methods
- `canBeBookedBy()`: Check if calendar accepts bookings from specific student
- `setDefaultSchedule()` / `removeSchedule()`: Schedule management
- `canAcceptBookings()`: Comprehensive booking permission check
- `findDefaultForUser()` / `findActiveForUser()`: Static methods for finding calendars

#### Enhanced Validation:
- Automatic cleanup of inappropriate fields based on owner type
- Ensures only one default calendar per user
- Proper timezone and access control validation

### 3. Enhanced Schedule Model Features

#### New Interface Properties:
- `studentBookings`: Track student interaction history with schedules
- Enhanced `settings` with calendar integration options

#### New Methods:
- `trackStudentBooking()`: Record when students book lessons
- `getStudentBookingHistory()`: Retrieve student's booking history
- `isCompatibleWithCalendar()`: Validate schedule-calendar compatibility
- `generateCalendarEventTitle()`: Generate consistent event titles

#### Enhanced Validation:
- Better time slot overlap detection
- Schedule type validation ensures corresponding data exists
- Calendar compatibility checks

### 4. Improved Lesson Model Integration

#### New Features:
- Enhanced indexing for better query performance
- Automatic schedule booking tracking via post-save hooks
- Calendar-based lesson queries

#### New Methods:
- `updateScheduleBookingTracker()`: Update schedule with student booking info
- `findByCalendar()`: Find lessons associated with specific calendar

### 5. Validation Utilities

#### Created: `src/utils/calendarScheduleValidation.ts`
Comprehensive validation functions:
- `validateCalendarScheduleRelationship()`: Ensure calendar-schedule consistency
- `validateScheduleData()`: Validate schedule configuration
- `validateLessonBooking()`: Validate lesson bookings against availability
- `validateCalendarData()`: Validate calendar creation data

### 6. Removed Duplicates
- **Removed**: `src/models/lesson.model.ts` (simpler duplicate)
- **Kept**: `src/models/Lesson.ts` (comprehensive version with better integration)

## Key Improvements

### 1. Data Consistency
- Single source of truth for calendar functionality
- Proper validation prevents invalid configurations
- Automatic cleanup of inappropriate fields

### 2. Better Integration
- Schedules now track student interactions
- Calendars have proper access control
- Lessons automatically update schedule tracking

### 3. Enhanced Functionality
- Support for both student and tutor calendars in one model
- Better timezone handling
- Comprehensive booking validation

### 4. Performance Optimizations
- Enhanced indexing strategies
- Efficient query methods
- Reduced model complexity

## Usage Examples

### Creating a Tutor Calendar with Schedule
```typescript
// Create tutor calendar
const tutorCalendar = new Calendar({
  ownerId: tutorId,
  ownerType: 'tutor',
  name: 'My Teaching Calendar',
  timezone: 'America/New_York',
  isDefault: true,
  accessSettings: {
    allowPublicView: true,
    requireSubscription: false,
    allowTrialBookings: true
  }
});

// Create schedule and link to calendar
const schedule = new Schedule({
  tutorId: tutorId,
  calendarId: tutorCalendar._id,
  name: 'Weekly Schedule',
  scheduleType: 'weekly',
  weeklySchedule: [/* schedule data */]
});

// Link schedule to calendar
await tutorCalendar.setDefaultSchedule(schedule._id);
```

### Creating a Student Calendar
```typescript
const studentCalendar = new Calendar({
  ownerId: studentId,
  ownerType: 'student',
  name: 'My Learning Calendar',
  isDefault: true,
  studentPreferences: {
    defaultView: 'week',
    showTutorCalendars: true,
    reminderSettings: {
      enabled: true,
      minutesBefore: [15, 60]
    }
  }
});
```

### Validating Lesson Booking
```typescript
import { validateLessonBooking } from '../utils/calendarScheduleValidation';

const validation = await validateLessonBooking(
  studentId,
  tutorCalendarId,
  new Date('2024-01-15T10:00:00Z'),
  60 // duration in minutes
);

if (!validation.isValid) {
  throw new Error(`Booking invalid: ${validation.errors.join(', ')}`);
}
```

## Migration Notes

### For Existing Code:
1. Replace references to `StudentCalendar` with `Calendar`
2. Update queries to use `ownerType` field for filtering
3. Use new validation utilities for data consistency
4. Update calendar creation to use consolidated model

### Database Migration:
1. Migrate existing `StudentCalendar` documents to `Calendar` collection
2. Add `ownerType: 'student'` to migrated documents
3. Update references in existing lessons and other models
4. Remove old `StudentCalendar` collection

## Benefits

1. **Reduced Complexity**: Single calendar model handles all use cases
2. **Better Data Integrity**: Comprehensive validation prevents inconsistencies
3. **Enhanced Functionality**: Rich feature set for both students and tutors
4. **Improved Performance**: Better indexing and query optimization
5. **Future-Proof**: Extensible design for additional features

## Next Steps

1. Update controllers to use new model structure
2. Update frontend to work with consolidated calendar model
3. Implement database migration scripts
4. Update API documentation
5. Add comprehensive tests for new functionality
